#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add the web_content_parser tool to the database.
"""

import sys
import os
from datetime import datetime

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(__file__))

from app.database.models import ToolRegistry
from app.database.connection import get_db_session
from app.tools.web_content_parser import web_content_parser

def add_web_content_parser_tool():
    """Add the web_content_parser tool to the database."""
    
    # Get database session
    db = next(get_db_session())
    
    try:
        # Check if tool already exists
        existing_tool = db.query(ToolRegistry).filter(
            ToolRegistry.name == "web_content_parser"
        ).first()
        
        if existing_tool:
            print("Tool 'web_content_parser' already exists in database")
            return
        
        # Get tool specification
        tool_spec = web_content_parser.spec
        
        # Create new tool registry entry
        new_tool = ToolRegistry(
            name="web_content_parser",
            description=tool_spec.description,
            tool_type="parser",
            category="web",
            module_path="app.tools.web_content_parser",
            class_name="WebContentParserTool",
            parameters_schema=tool_spec.input_schema,
            output_schema=tool_spec.output_schema,
            version="1.0.0",
            author="AgentKit",
            tags=["web", "parser", "content", "scraping", "trafilatura", "playwright"],
            is_global=True,
            is_active=True
        )
        
        # Add to database
        db.add(new_tool)
        db.commit()
        
        print("Successfully added 'web_content_parser' tool to database")
        print(f"Tool ID: {new_tool.id}")
        print(f"Name: {new_tool.name}")
        print(f"Description: {new_tool.description}")
        print(f"Tool Type: {new_tool.tool_type}")
        print(f"Category: {new_tool.category}")
        print(f"Module Path: {new_tool.module_path}")
        print(f"Class Name: {new_tool.class_name}")
        print(f"Version: {new_tool.version}")
        print(f"Author: {new_tool.author}")
        print(f"Tags: {new_tool.tags}")
        print(f"Is Global: {new_tool.is_global}")
        print(f"Is Active: {new_tool.is_active}")
        
    except Exception as e:
        print(f"Error adding tool to database: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    add_web_content_parser_tool()
