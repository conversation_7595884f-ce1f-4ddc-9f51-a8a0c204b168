Metadata-Version: 2.4
Name: agentkit
Version: 0.1.0
Summary: Modular Agentic AI starter framework (planner, tools, memory, guards, interface).
Requires-Python: >=3.10
Description-Content-Type: text/markdown
Requires-Dist: fastapi>=0.115.0
Requires-Dist: uvicorn[standard]>=0.30.0
Requires-Dist: pydantic>=2.7.0
Requires-Dist: pydantic-settings>=2.2.1
Requires-Dist: httpx>=0.27.0
Requires-Dist: PyYAML>=6.0.1
Requires-Dist: loguru>=0.7.2
Requires-Dist: rich>=13.7.1
Requires-Dist: typing-extensions>=4.12.0
Requires-Dist: qdrant-client>=1.7.0
Requires-Dist: redis>=5.0.4
Requires-Dist: pymongo>=4.7.2
Requires-Dist: psycopg2>=2.9.9
Requires-Dist: boto3>=1.34.0
Requires-Dist: langchain>=0.1.0
Requires-Dist: langchain-core>=0.1.0
Requires-Dist: langchain-community>=0.0.10
Requires-Dist: sentence-transformers>=2.2.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: ddgs>=5.0.0
Requires-Dist: markdown>=3.5.0
Requires-Dist: weasyprint>=60.0
Requires-Dist: reportlab>=4.0.0
Requires-Dist: trafilatura>=1.6.0
Requires-Dist: playwright>=1.40.0
Provides-Extra: openai
Requires-Dist: openai>=1.40.0; extra == "openai"
Provides-Extra: mistral
Requires-Dist: mistralai>=0.4.0; extra == "mistral"
Provides-Extra: gemini
Requires-Dist: google-generativeai>=0.7.2; extra == "gemini"
Provides-Extra: zhipu
Requires-Dist: zhipuai>=2.0.0; extra == "zhipu"
Provides-Extra: ollama
Requires-Dist: ollama>=0.3.0; extra == "ollama"

# AgentKit - Agentic AI Framework

A comprehensive, production-ready Agentic AI framework with **Configuration-Driven Agent Creation**, **Multi-Agent Orchestration**, and **Enterprise-Grade Features**.

## 🎨 New Frontend Application

AgentKit now includes a modern **React.js/TypeScript frontend** with professional UI/UX and comprehensive features:

### Frontend Features
- 🤖 **Agent Management**: Create, configure, and manage AI agents with intuitive forms
- 💬 **Chat Interface**: Real-time chat with agents and conversation history
- 🧠 **Memory Viewing**: Explore all types of agent memory (short-term, episodic, procedural, semantic)
- 📱 **Responsive Design**: Modern, professional UI that works on all devices
- 🌙 **Dark/Light Theme**: Toggle between themes with system preference support
- ⚡ **Real-time Updates**: Optimized with React Query for efficient data fetching
- 🔍 **Search & Filter**: Advanced search and filtering capabilities
- 📊 **Dashboard**: Overview with key metrics and recent activity

### Quick Start with Frontend

1. **Start the Backend API**:
   ```bash
   python -m uvicorn app.api.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Start the Frontend**:
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

3. **Access the Application**:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## 🚀 Phase 1 Features

### ✅ Core Framework
- **Agent Factory Pattern**: Create specialized agents (Customer Support, Sales, Research) with configuration-driven setup
- **Multi-Agent Orchestration**: Coordinate multiple agents for complex workflows
- **Production-Ready API**: RESTful endpoints with comprehensive error handling and validation
- **Memory Systems**: In-memory buffer, persistent JSONL storage, and Qdrant vector database integration
- **Tool Registry**: Extensible tool system with HTTP and math evaluation tools
- **Validation Framework**: Step limits, token budgets, and content filtering
- **Policy Management**: YAML/JSON configuration for agent behavior and constraints
- **LLM Provider Support**: OpenAI, Ollama, and extensible provider architecture

### ✅ Specialized Agents
- **Customer Support Agent**: Ticket management, sentiment analysis, escalation handling
- **Sales Agent**: Lead qualification, product recommendations, pricing negotiations
- **Research Agent**: Multi-source information gathering, fact verification, report generation

### ✅ API Endpoints
- **Agent Management**: Create, list, update, delete agents
- **Configuration Management**: Dynamic agent configuration updates
- **Health Monitoring**: Agent health checks and status monitoring
- **Bulk Operations**: Bulk agent creation and management
- **Multi-Agent Collaboration**: Coordinate multiple agents for complex tasks

### ✅ Enterprise Features
- **Centralized Configuration**: Pydantic-based settings with environment variable support
- **Production-Ready Logging**: Structured logging with correlation IDs and context tracking
- **Enhanced Error Handling**: Hierarchical exceptions with detailed context and HTTP status mapping
- **Comprehensive Testing**: 100+ unit tests and integration tests with settings validation
- **Configuration Validation**: Schema validation for agent configurations and environment settings
- **Documentation**: Complete API documentation, configuration reference, and examples

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Layer    │    │  Agent Factory  │    │ Specialized     │
│                │    │                 │    │ Agents          │
│ • REST API     │────│ • Agent Types   │────│ • Customer      │
│ • Validation   │    │ • Configuration │    │   Support       │
│ • Error        │    │ • Registration  │    │ • Sales         │
│   Handling     │    │                 │    │ • Research      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │  Core Runner    │              │
         └──────────────│                 │──────────────┘
                        │ • Planning      │
                        │ • Execution     │
                        │ • State Mgmt    │
                        └─────────────────┘
                                 │
    ┌─────────────┬──────────────┼──────────────┬─────────────┐
    │             │              │              │             │
┌───▼───┐  ┌─────▼─────┐  ┌─────▼─────┐  ┌─────▼─────┐  ┌───▼───┐
│ Tools │  │  Memory   │  │Validation │  │ Policies  │  │ LLM   │
│       │  │           │  │           │  │           │  │Providers│
│• HTTP │  │• Buffer   │  │• Limits   │  │• YAML     │  │• OpenAI│
│• Math │  │• JSONL    │  │• Content  │  │• JSON     │  │• Ollama│
└───────┘  └───────────┘  └───────────┘  └───────────┘  └───────┘
```

## 📦 Installation

### Prerequisites
- Python 3.10+
- Virtual environment (recommended)

### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd agentkit

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -e ".[openai]"

# Set up environment
cp .env.example .env
# Edit .env with your API keys

# Run the application
make run
# or
uvicorn app.api.main:app --host 0.0.0.0 --port 8000 --reload

# Open API documentation
# http://localhost:8000/docs
```

### Optional Dependencies
```bash
# For different LLM providers
pip install -e ".[openai]"     # OpenAI GPT models
pip install -e ".[ollama]"     # Ollama local models
pip install -e ".[mistral]"    # Mistral AI models
pip install -e ".[gemini]"     # Google Gemini models
pip install -e ".[zhipu]"      # ZhipuAI models

# For development
pip install -e ".[dev]"        # Development dependencies

# Additional tools
pip install requests           # Required for SearxNG web search tool
```

## ⚙️ Configuration

AgentKit uses a comprehensive Pydantic-based configuration system that supports environment variables, validation, and production-ready defaults. The configuration is centralized in `app/core/settings.py` and automatically loads from environment variables and `.env` files.

### Configuration Architecture

- **Type-Safe Configuration**: All settings are validated using Pydantic models
- **Environment Variable Support**: Automatic loading from environment variables and `.env` files
- **Production Validation**: Enhanced validation for production environments
- **Hierarchical Settings**: Organized into logical groups (LLM, Database, API, etc.)
- **Default Values**: Sensible defaults for development with production overrides

### Environment Variables

Create a `.env` file in the project root with your configuration:

```bash
# =============================================================================
# AGENTKIT CONFIGURATION
# =============================================================================

# Environment
ENVIRONMENT=development                 # development, testing, staging, production

# =============================================================================
# LLM PROVIDER CONFIGURATION
# =============================================================================

# Provider Selection
LLM_PROVIDER=openai                    # openai, mistral, gemini, zhipu, ollama

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key     # Required for OpenAI
OPENAI_MODEL=gpt-4o-mini               # gpt-4o-mini, gpt-4o, gpt-3.5-turbo
OPENAI_BASE_URL=                       # Optional custom API base URL

# Mistral Configuration
MISTRAL_API_KEY=your_mistral_api_key   # Required for Mistral
MISTRAL_MODEL=mistral-large-latest     # mistral-large-latest, mistral-medium

# Google Gemini Configuration
GEMINI_API_KEY=your_gemini_api_key     # Required for Gemini
GEMINI_MODEL=gemini-pro                # gemini-pro, gemini-pro-vision

# ZhipuAI Configuration
ZHIPU_API_KEY=your_zhipu_api_key       # Required for ZhipuAI
ZHIPU_MODEL=glm-4                      # glm-4, glm-3-turbo

# Ollama Configuration (Local)
OLLAMA_MODEL=llama3.2:3b               # Model name for Ollama
OLLAMA_HOST=http://localhost:11434     # Ollama server URL

# LLM Parameters
LLM_TEMPERATURE=0.7                    # 0.0-2.0, creativity level
LLM_MAX_TOKENS=2000                    # Maximum tokens per response
LLM_TIMEOUT=30.0                       # Request timeout in seconds

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Redis Configuration
REDIS_HOST=localhost                   # Redis server host
REDIS_PORT=6379                        # Redis server port
REDIS_PASSWORD=                        # Redis password (optional)
REDIS_DB=0                            # Redis database number

# Qdrant Vector Database
QDRANT_HOST=localhost                  # Qdrant server host
QDRANT_PORT=6333                       # Qdrant server port
QDRANT_API_KEY=                        # Qdrant API key (optional)

# PostgreSQL Configuration
POSTGRES_HOST=localhost                # PostgreSQL server host
POSTGRES_PORT=5432                     # PostgreSQL server port
POSTGRES_DB=agentkit                   # Database name
POSTGRES_USER=postgres                 # Database user
POSTGRES_PASSWORD=                     # Database password

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

LOG_LEVEL=INFO                         # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FORMAT=json                        # json, text
LOG_STRUCTURED_LOGGING=true            # Enable structured logging
LOG_CORRELATION_ID=true                # Enable correlation ID tracking
LOG_FILE_LOGGING=false                 # Enable file logging
LOG_FILE_PATH=./logs/agentkit.log      # Log file path
LOG_MAX_FILE_SIZE=10MB                 # Maximum log file size
LOG_BACKUP_COUNT=5                     # Number of backup log files
LOG_CONSOLE_LOGGING=true               # Enable console logging

# =============================================================================
# API CONFIGURATION
# =============================================================================

API_HOST=0.0.0.0                       # API server host
API_PORT=8000                          # API server port
API_DEBUG=false                        # Enable debug mode
API_RELOAD=false                       # Enable auto-reload
API_CORS_ENABLED=true                  # Enable CORS
API_CORS_ORIGINS=["*"]                 # Allowed CORS origins (JSON array)
API_DOCS_ENABLED=true                  # Enable API documentation
API_DOCS_URL=/docs                     # Swagger UI URL
API_REDOC_URL=/redoc                   # ReDoc URL
API_OPENAPI_URL=/openapi.json          # OpenAPI schema URL

# Rate Limiting
API_RATE_LIMIT_ENABLED=true            # Enable rate limiting
API_RATE_LIMIT_REQUESTS=100            # Requests per window
API_RATE_LIMIT_WINDOW=60               # Rate limit window (seconds)

# =============================================================================
# MEMORY CONFIGURATION
# =============================================================================

MEMORY_DEFAULT_ADAPTER=in_memory       # in_memory, redis, sqlite, jsonl, vectordb
MEMORY_MAX_SIZE=1000                   # Maximum memory items per key
MEMORY_DATA_DIRECTORY=./data           # Data directory for file-based storage
MEMORY_MAX_FILE_SIZE_MB=100            # Maximum file size for JSONL storage
MEMORY_LANGCHAIN_ENABLED=true          # Enable LangChain memory integration
MEMORY_CLEANUP_ENABLED=true            # Enable automatic memory cleanup
MEMORY_CLEANUP_INTERVAL_HOURS=24       # Cleanup interval in hours
MEMORY_RETENTION_DAYS=30               # Memory retention period

# =============================================================================
# OBSERVABILITY CONFIGURATION
# =============================================================================

OBSERVABILITY_METRICS_ENABLED=true     # Enable metrics collection
OBSERVABILITY_METRICS_PORT=9090        # Metrics server port
OBSERVABILITY_TRACING_ENABLED=false    # Enable distributed tracing
OBSERVABILITY_TRACING_ENDPOINT=         # Jaeger tracing endpoint
OBSERVABILITY_HEALTH_CHECK_ENABLED=true # Enable health checks
OBSERVABILITY_HEALTH_CHECK_INTERVAL=30  # Health check interval (seconds)

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

SECRET_KEY=your-secret-key-change-in-production  # JWT secret key (CHANGE IN PRODUCTION!)
JWT_ALGORITHM=HS256                    # JWT signing algorithm
JWT_EXPIRATION_HOURS=24                # JWT token expiration
CORS_ALLOW_CREDENTIALS=false           # Allow credentials in CORS
RATE_LIMITING_ENABLED=true             # Enable global rate limiting
API_KEY_HEADER=X-API-Key               # API key header name

# =============================================================================
# AGENT CONFIGURATION
# =============================================================================

AGENT_MAX_ITERATIONS=10                # Maximum agent iterations
AGENT_TIMEOUT=300                      # Agent timeout in seconds
AGENT_MEMORY_ENABLED=true              # Enable agent memory
AGENT_TOOLS_ENABLED=true               # Enable agent tools
AGENT_PLANNER_TYPE=default             # Agent planner type
AGENT_DEFAULT_TEMPERATURE=0.7          # Default agent temperature
```

### Configuration Validation

AgentKit automatically validates all configuration settings using Pydantic models. The validation includes:

- **Type Checking**: Ensures all values are of the correct type
- **Range Validation**: Validates numeric ranges (e.g., ports, temperatures)
- **Format Validation**: Validates URLs, file paths, and other formats
- **Cross-Field Validation**: Validates dependencies between settings
- **Production Constraints**: Enhanced validation for production environments

#### Validation Examples

```python
# Valid configuration
REDIS_PORT=6379                        # ✅ Valid port number
LLM_TEMPERATURE=0.7                    # ✅ Valid temperature range (0.0-2.0)
LOG_LEVEL=INFO                         # ✅ Valid log level

# Invalid configuration
REDIS_PORT=99999                       # ❌ Port out of range
LLM_TEMPERATURE=3.0                    # ❌ Temperature out of range
LOG_LEVEL=INVALID                      # ❌ Invalid log level
```

### Configuration Best Practices

#### Development Environment
```bash
ENVIRONMENT=development
API_DEBUG=true
LOG_LEVEL=DEBUG
LOG_CONSOLE_LOGGING=true
MEMORY_DEFAULT_ADAPTER=in_memory
```

#### Production Environment
```bash
ENVIRONMENT=production
API_DEBUG=false                        # Must be false in production
LOG_LEVEL=INFO                         # Should not be DEBUG in production
SECRET_KEY=your-secure-production-key  # Must be changed from default
OPENAI_API_KEY=your-production-api-key # Required for OpenAI provider
MEMORY_DEFAULT_ADAPTER=redis           # Use persistent storage
OBSERVABILITY_METRICS_ENABLED=true     # Enable monitoring
```

#### Security Considerations
- **Never commit API keys** to version control
- **Use strong secret keys** in production (minimum 32 characters)
- **Enable rate limiting** in production environments
- **Use HTTPS** for all external API calls
- **Restrict CORS origins** to specific domains in production

### Settings Usage in Code

```python
from app.core.settings import get_settings

# Get settings instance (cached)
settings = get_settings()

# Access configuration
print(f"Using LLM provider: {settings.llm.provider}")
print(f"API running on port: {settings.api.port}")
print(f"Database URL: {settings.database.redis_url}")

# Check environment
if settings.is_production:
    print("Running in production mode")
```

## Tools Management

The AgentKit platform includes a comprehensive tools management system that allows you to:

### Features

- **View Registered Tools**: Browse all available tools with their specifications
- **Tool Testing**: Execute tools with sample inputs and view outputs
- **Tool Creation**: Create new custom tools with JSON schema definitions
- **Category Management**: Organize tools by categories (network, conversion, utility, etc.)
- **Usage Analytics**: Track tool usage statistics and execution history

### Available Tools

The platform comes with several built-in tools:

- **HTTP GET**: Fetch web content via HTTP requests
- **Markdown to PDF**: Convert markdown content to professionally formatted PDFs
- **SearxNG Search**: Web search using the SearxNG metasearch engine
- **Math Evaluator**: Evaluate mathematical expressions safely
- **DuckDuckGo Search**: Web search using DuckDuckGo API

### Tool Development

#### Creating Custom Tools

1. Navigate to the Tools page in the UI
2. Click "Create Tool" 
3. Define the tool specification:
   - Name and description
   - Input schema (JSON Schema format)
   - Output schema (JSON Schema format)
   - Category classification

#### Tool Specification Format

```json
{
  "name": "my_custom_tool",
  "description": "Description of what the tool does",
  "input_schema": {
    "type": "object",
    "properties": {
      "param1": {
        "type": "string",
        "description": "First parameter"
      }
    },
    "required": ["param1"]
  },
  "output_schema": {
    "type": "object",
    "properties": {
      "result": {
        "type": "string",
        "description": "Tool output"
      }
    }
  }
}
```

### API Endpoints

- `GET /api/tools` - List all tools
- `GET /api/tools/{name}` - Get specific tool
- `POST /api/tools/execute` - Execute a tool
- `POST /api/tools` - Create new tool
- `GET /api/tools/categories` - Get tool categories

### Testing Tools

Use the built-in tool testing interface to:

1. Select a tool from the list
2. Enter JSON input parameters
3. Execute the tool and view results
4. Analyze execution time and success/failure status

This ensures tools work correctly before using them in agent workflows.

### Agent Configuration Files
Create YAML or JSON configuration files for custom agent behavior:

```yaml
# config/customer_support.yaml
tools:
  - "http.get"
  - "knowledge_base.search"
memory:
  type: "buffer"
  max_size: 1000
planner:
  type: "react"
  max_steps: 10
escalation_threshold: 0.3
max_resolution_attempts: 3
supported_languages:
  - "en"
  - "es"
  - "fr"
```

## 📊 Production-Ready Logging

AgentKit features a comprehensive logging system with structured logging, correlation IDs, and context tracking for production environments.

### Logging Features

- **Structured Logging**: JSON-formatted logs with consistent schema
- **Correlation IDs**: Track requests across services and components
- **Context Tracking**: Automatic context propagation for agent operations
- **Performance Monitoring**: Built-in performance tracking and slow query detection
- **Multiple Outputs**: Console, file, and external logging service support
- **Log Levels**: Configurable log levels with environment-specific defaults

### Logging Configuration

```bash
# Basic Logging
LOG_LEVEL=INFO                         # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FORMAT=json                        # json, text
LOG_STRUCTURED_LOGGING=true            # Enable structured logging

# Advanced Features
LOG_CORRELATION_ID=true                # Enable correlation ID tracking
LOG_FILE_LOGGING=true                  # Enable file logging
LOG_FILE_PATH=./logs/agentkit.log      # Log file path
LOG_MAX_FILE_SIZE=10MB                 # Maximum log file size
LOG_BACKUP_COUNT=5                     # Number of backup log files
```

### Structured Log Format

```json
{
  "timestamp": "2024-01-15T10:30:45.123Z",
  "level": "INFO",
  "logger": "app.agents.customer_support",
  "message": "Agent processing user request",
  "correlation_id": "req_abc123def456",
  "context": {
    "agent_name": "support_agent_1",
    "user_id": "user_123",
    "session_id": "session_456",
    "operation": "process_request"
  },
  "performance": {
    "duration_ms": 245,
    "memory_usage_mb": 12.5
  },
  "metadata": {
    "environment": "production",
    "version": "1.0.0",
    "service": "agentkit"
  }
}
```

### Using Logging in Code

```python
from app.observability.logging import get_logger, log_agent_action, performance_monitor

# Get logger instance
logger = get_logger(__name__)

# Basic logging
logger.info("Agent started successfully", extra={
    "agent_name": "customer_support",
    "agent_type": "support"
})

# Agent-specific logging
log_agent_action(
    agent_name="support_agent_1",
    action="process_request",
    user_id="user_123",
    duration_ms=245
)

# Performance monitoring decorator
@performance_monitor("agent_operation")
def process_user_request(request):
    # Your code here
    pass
```

### Correlation ID Tracking

Correlation IDs automatically track requests across the entire system:

```python
# Correlation IDs are automatically generated and propagated
# Request: req_abc123def456

# All logs for this request will include the same correlation_id
logger.info("Starting agent processing")     # correlation_id: req_abc123def456
logger.info("Calling LLM provider")         # correlation_id: req_abc123def456
logger.info("Storing in memory")            # correlation_id: req_abc123def456
logger.info("Request completed")            # correlation_id: req_abc123def456
```

### Log Analysis and Monitoring

#### Development Environment
```bash
# View logs in real-time
tail -f logs/agentkit.log | jq '.'

# Filter by log level
tail -f logs/agentkit.log | jq 'select(.level == "ERROR")'

# Filter by correlation ID
tail -f logs/agentkit.log | jq 'select(.correlation_id == "req_abc123def456")'
```

#### Production Environment
- **ELK Stack**: Elasticsearch, Logstash, and Kibana for log aggregation
- **Grafana**: Dashboards for log visualization and alerting
- **Prometheus**: Metrics collection from structured logs
- **Jaeger**: Distributed tracing integration

### Error Handling and Logging

```python
from app.core.exceptions import AgentKitError
from app.observability.logging import get_logger

logger = get_logger(__name__)

try:
    # Agent operation
    result = agent.process_request(request)
except AgentKitError as e:
    # Structured error logging
    logger.error("Agent operation failed", extra={
        "error_code": e.error_code,
        "error_context": e.context,
        "correlation_id": e.correlation_id,
        "agent_name": agent.name,
        "operation": "process_request"
    })
    raise
```

## 🎭 Enhanced Agent Features

### Agent Personas and System Prompts

Each agent now comes with a detailed persona and system prompt for more natural interactions:

```yaml
# Enhanced agent configuration
persona: |
  You are Alex, a friendly and professional customer support specialist with 5+ years of experience.
  You are empathetic, patient, and solution-oriented.

system_prompt: |
  You are a professional customer support agent. Your role is to:
  1. Listen actively and understand customer issues
  2. Show empathy and validate concerns
  3. Provide clear solutions and actionable steps
  4. Be proactive and anticipate follow-up questions
  5. Escalate when needed

# LLM Provider Configuration
llm_provider: "ollama"
llm_model: "gemma3:270m"
```

### Session-Based Memory Management

Agents now maintain conversation context across interactions:

```python
# Chat with session continuity
response = requests.post("http://localhost:8000/agents/{agent_id}/chat", json={
    "message": "Hello, I need help with my order",
    "session_id": "user_123_session",  # Optional - auto-generated if not provided
    "context": {"user_id": "123", "order_id": "ORD-456"}
})
```

## 🧠 Comprehensive Memory System ✅ FULLY FUNCTIONAL

AgentKit features a sophisticated multi-layered memory architecture designed for production-ready agent applications with four distinct memory types and multiple storage adapters. **All memory backends are now fully functional and tested.**

### Memory Architecture Overview

The memory system provides a unified interface for different types of memory storage with complete persistence across Redis, SQLite, VectorDB, and file-based backends:

```
┌─────────────────────────────────────────────────────────────┐
│                    Memory Manager                           │
├─────────────────┬─────────────────┬─────────────────┬───────┤
│  Short-term     │   Episodic      │   Procedural    │Semantic│
│   Memory        │    Memory       │    Memory       │Memory │
│                 │                 │                 │       │
│• Conversation   │• Event Logs     │• Workflows      │• Facts│
│• Session Context│• Interactions   │• Procedures     │• Search│
│• Temporary Data │• User Behavior  │• Step-by-step   │• Knowledge│
└─────────────────┴─────────────────┴─────────────────┴───────┘
         │                 │                 │           │
┌────────▼─────────────────▼─────────────────▼───────────▼────┐
│                 Storage Adapters                            │
│ InMemory │ JSONL │ SQLite │ Redis │ VectorDB │ S3 │ MongoDB │
└─────────────────────────────────────────────────────────────┘
```

### Memory Types

#### 🔄 Short-term Memory
- **Purpose**: Recent conversation history and session context
- **Scope**: Agent + User + Session specific
- **Retention**: Session-based (cleared when session ends)
- **Use Cases**: Chat history, current conversation context, temporary variables

#### 📚 Episodic Memory
- **Purpose**: Event logging and experience tracking
- **Scope**: Agent + User + Session specific
- **Retention**: Persistent across sessions
- **Use Cases**: Interaction logs, user behavior patterns, task completion history

#### ⚙️ Procedural Memory
- **Purpose**: Process and workflow storage
- **Scope**: Agent + User specific (shared across sessions)
- **Retention**: Long-term persistent
- **Use Cases**: Learned procedures, custom workflows, step-by-step guides

#### 🔍 Semantic Memory
- **Purpose**: Knowledge and fact storage with semantic search
- **Scope**: Agent + User specific (shared across sessions)
- **Retention**: Long-term persistent
- **Use Cases**: Facts, concepts, knowledge base, semantic search

### Memory Adapters

#### Available Storage Backends

| Adapter | Use Case | Persistence | Performance | Scalability |
|---------|----------|-------------|-------------|-------------|
| **InMemory** | Development, Testing | ❌ | ⚡ Very Fast | 🔸 Single Process |
| **JSONL** | Simple Persistence | ✅ | 🟡 Fast | 🔸 Single Node |
| **SQLite** | Local Database | ✅ | 🟡 Fast | 🔸 Single Node |
| **Redis** | Distributed Cache | ✅ | ⚡ Very Fast | 🟢 Clustered |
| **VectorDB** | Semantic Search | ✅ | 🟡 Fast | 🟢 Clustered |
| **PostgreSQL** | Enterprise DB | ✅ | 🟡 Fast | 🟢 Clustered |
| **MongoDB** | Document Store | ✅ | 🟡 Fast | 🟢 Clustered |
| **S3** | Cloud Storage | ✅ | 🔴 Slow | 🟢 Unlimited |

#### ✅ Current Implementation Status

All core memory adapters are **fully functional and tested**:

- **✅ InMemoryAdapter**: Complete with session isolation and message handling
- **✅ RedisAdapter**: Full Redis integration with database separation and key structuring
- **✅ SQLiteAdapter**: Complete SQLite implementation with JSON serialization
- **✅ VectorDBAdapter**: Full Qdrant integration with HTTP fallback
- **✅ JSONLAdapter**: Complete file-based storage with line-by-line JSON

#### Agent-Specific Memory Configurations

Each agent type uses different memory backends for optimal performance:

**🔬 Research Agent:**
- Short-term: Redis (Database 1) - Fast distributed access
- Episodic: Redis (Database 2) - Persistent event logging
- Procedural: SQLite - Structured procedure storage
- Semantic: VectorDB - Semantic search capabilities

**💼 Sales Agent:**
- Short-term: InMemory - Fast session-based interactions
- Episodic: JSONL - Human-readable interaction logs
- Procedural: SQLite - Structured sales processes
- Semantic: VectorDB - Product knowledge and recommendations

**🎧 Customer Support Agent:**
- Short-term: InMemory - Fast ticket handling
- Episodic: SQLite - Structured support history
- Procedural: JSONL - Flexible support procedures
- Semantic: VectorDB - Knowledge base search

### Memory Monitoring Tools

AgentKit includes comprehensive monitoring tools for all memory backends:

```bash
# View complete memory system status
python tools/memory_monitor.py summary

# View Redis data for specific agent
python tools/redis_viewer.py agent research_agent user123

# Export detailed statistics
python tools/memory_monitor.py export --output memory_stats.json

# Clean up test data across all backends
python tools/memory_monitor.py cleanup
```

### Configuration Examples

#### Development Configuration
```python
memory_config = {
    "short_term": {"adapter": "in_memory"},
    "episodic": {"adapter": "in_memory"},
    "procedural": {"adapter": "in_memory"},
    "semantic": {"adapter": "in_memory"}
}
```

#### Production Configuration
```python
memory_config = {
    "short_term": {
        "adapter": "redis",
        "host": "redis-cluster.internal",
        "port": 6379,
        "ttl": 3600  # 1 hour session timeout
    },
    "episodic": {
        "adapter": "postgresql",
        "connection_string": "******************************/episodic"
    },
    "procedural": {
        "adapter": "sqlite",
        "db_path": "./data/procedures.db"
    },
    "semantic": {
        "adapter": "vectordb",
        "url": "http://qdrant-cluster:6333",
        "collection": "agent_knowledge"
    }
}
```

### Vector Database Memory (Qdrant Integration)

Enhanced semantic memory with vector database support:

```bash
# Set up Qdrant for vector memory
docker run -d -p 6333:6333 qdrant/qdrant

# Configure agent with comprehensive memory
curl -X POST "http://localhost:8000/agent-factory/agents/create" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_type": "customer_support",
    "name": "comprehensive_memory_agent",
    "config": {
      "memory": {
        "short_term": {"adapter": "in_memory"},
        "episodic": {"adapter": "jsonl", "jsonl_path": "./logs/episodes.jsonl"},
        "procedural": {"adapter": "sqlite", "db_path": "./data/procedures.db"},
        "semantic": {
          "adapter": "vectordb",
          "url": "http://localhost:6333",
          "collection": "agent_knowledge",
          "vector_size": 768,
          "distance": "Cosine"
        }
      }
    }
  }'
```

**Enhanced Memory Features:**
- **Multi-Type Integration**: All four memory types working together
- **Semantic Search**: Vector-based similarity search for knowledge retrieval
- **Persistent Storage**: Long-term memory that survives agent restarts
- **HTTP Fallback**: Automatic fallback to HTTP API when client methods fail
- **Session Isolation**: Separate short-term memory per session
- **Cross-Session Persistence**: Episodic, procedural, and semantic memory shared across sessions
- **Error Resilience**: Comprehensive error handling with graceful degradation
- **Production Ready**: Scalable architecture with proper logging and monitoring

## 🚀 Intelligent Memory System (Latest Update)

The AgentKit framework now features an advanced AI-powered memory system with LLM categorization, semantic embeddings, and intelligent storage management.

### 🆕 Intelligent Memory Features

#### LLM-Powered Categorization
- **Ollama Integration**: Uses gemma3:270m model for automatic message categorization
- **Smart Routing**: Automatically routes messages to appropriate memory types (episodic, procedural, preference, fact, semantic)
- **Confidence Scoring**: Each memory item has a confidence score for quality assessment
- **Fallback Processing**: Graceful degradation when LLM services are unavailable

#### Semantic Embeddings & RAG
- **sentence-transformers**: Uses all-MiniLM-L6-v2 for semantic understanding
- **Vector Storage**: Enhanced VectorDB adapter with 384-dimensional embeddings
- **RAG Retrieval**: Semantic search for relevant context during planning
- **User Facts Extraction**: Automatically extracts user facts to enrich system prompts

#### Advanced Memory Management
- **Content Deduplication**: SHA-256 hashing prevents duplicate storage
- **Confidence Decay**: Time-based confidence scoring with automatic cleanup
- **Memory Cleanup**: Automatic removal of low-confidence memories
- **Cross-session Intelligence**: Smart memory persistence across conversations

#### LangChain Integration
- **Token-aware Memory**: ConversationTokenBufferMemory for efficient token management
- **Memory Summarization**: ConversationSummaryBufferMemory for long conversations
- **Automatic Optimization**: Smart memory cleanup and token management

### Enhanced Configuration

```yaml
memory:
  short_term:
    adapter: "redis"
    redis_host: "localhost"
    redis_port: 6379
    redis_db: 1
    session_based: true  # Use session-based keys
  episodic:
    adapter: "redis"
    redis_host: "localhost"
    redis_port: 6379
    redis_db: 2
    session_based: true
  procedural:
    adapter: "sqlite"
    sqlite_path: "./data/procedures.sqlite"
    session_based: false  # Cross-session memory
  semantic:
    adapter: "vectordb"
    qdrant_host: "localhost"
    qdrant_port: 6333
    collection: "agent_knowledge"
    session_based: false

  # Enhanced memory features
  langchain:
    enabled: true
    token_buffer_memory:
      max_token_limit: 2000
      llm_model: "gpt-3.5-turbo"
    summary_buffer_memory:
      max_token_limit: 1000
      return_messages: true

  mem0:
    enabled: true
    api_key: "${MEM0_API_KEY}"
    user_id: "default_user"

  preferences:
    adapter: "mem0"  # Use Mem0 for preference management
    enabled: true
```

### Enhanced Memory Operations

#### Core Operations (Enhanced)
- `add_message(agent_id, user_id, message, session_id)`: Store conversation messages
- `get_messages(agent_id, user_id, session_id, limit)`: Retrieve conversation history
- `log_event(agent_id, user_id, session_id, event)`: Log interaction events
- `add_procedure(agent_id, user_id, name, procedure)`: Store learned procedures
- `add_fact(agent_id, user_id, fact)`: Store semantic facts
- `search_facts(agent_id, user_id, query, limit)`: Search semantic knowledge

#### New Enhanced Operations
- `add_preference(agent_id, user_id, preference)`: Store user/agent preferences
- `get_preferences(agent_id, user_id, filters)`: Retrieve preferences with filtering
- `update_preference(agent_id, user_id, preference_id, updates)`: Update existing preferences
- `delete_preference(agent_id, user_id, preference_id)`: Remove preferences

#### LangChain Operations
- `get_token_buffer_memory(agent_id, user_id, session_id)`: Get token-aware memory
- `get_summary_buffer_memory(agent_id, user_id, session_id)`: Get hybrid memory
- `optimize_memory(agent_id, user_id, session_id)`: Optimize memory usage

### Installation Requirements

Install additional dependencies for enhanced features:

```bash
pip install -r requirements_memory_enhanced.txt
```

This includes:
- `langchain`: LangChain memory integration
- `mem0ai`: Mem0 SDK for advanced memory management
- `qdrant-client`: Vector database client
- `redis`: Redis client

### Enhanced Testing

Run comprehensive enhanced memory tests:

```bash
# Enhanced memory system tests
python test_enhanced_memory_system.py

# All agents with enhanced memory
python test_all_agents_enhanced_memory.py

# Memory adapter fixes and validation
python test_memory_fixes.py
```

## 🧪 Comprehensive Testing Suite

AgentKit includes a comprehensive testing suite with unit tests, integration tests, and specialized validation tests for all components.

### Settings System Tests

Test the centralized configuration system:

```bash
# Run settings validation tests
python tests/test_settings_runner.py

# Quick validation only
python tests/test_settings_runner.py --quick

# Full test suite with coverage
python tests/test_settings_runner.py --coverage --verbose

# Run specific settings tests
pytest tests/unit/test_settings.py -v
```

### Test Categories

#### Unit Tests
- **Settings Validation**: Test all configuration classes and validation logic
- **Provider Factory**: Test LLM provider creation and configuration
- **Memory Adapters**: Test all memory storage backends
- **Agent Factory**: Test agent creation and configuration
- **Exception Handling**: Test error handling and exception hierarchy

#### Integration Tests
- **API Endpoints**: Test all REST API endpoints with real configurations
- **Memory Integration**: Test memory system with different storage backends
- **Agent Workflows**: Test complete agent workflows with real LLM providers
- **Configuration Loading**: Test environment variable loading and validation

#### Performance Tests
- **Settings Loading**: Validate settings loading performance
- **Memory Operations**: Test memory adapter performance
- **Agent Response Times**: Measure agent processing performance
- **Concurrent Operations**: Test system under concurrent load

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test categories
pytest tests/unit/ -v                  # Unit tests only
pytest tests/integration/ -v           # Integration tests only

# Run tests for specific components
pytest tests/unit/test_settings.py     # Settings tests
pytest tests/unit/test_memory.py       # Memory tests
pytest tests/unit/test_agents.py       # Agent tests

# Run tests with specific markers
pytest -m "not slow"                   # Skip slow tests
pytest -m "integration"                # Run integration tests only
```

### Test Configuration

Create a test-specific `.env.test` file:

```bash
# Test Environment Configuration
ENVIRONMENT=testing
LOG_LEVEL=DEBUG
MEMORY_DEFAULT_ADAPTER=in_memory
API_PORT=8001
REDIS_DB=1                             # Use different Redis DB for tests
POSTGRES_DB=agentkit_test              # Use test database
```

### Continuous Integration

The test suite is designed for CI/CD environments:

```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.10'
      - name: Install dependencies
        run: |
          pip install -e ".[dev]"
      - name: Run tests
        run: |
          pytest --cov=app --cov-report=xml
      - name: Upload coverage
        uses: codecov/codecov-action@v1
```

### Performance Optimizations

#### Key Structure Optimization
- Session-based keys only when needed
- Reduced key complexity
- Efficient memory routing

#### Memory Management
- Automatic token-based pruning
- Smart memory summarization
- Cross-session memory sharing
- Preference-based optimization

### Migration Guide

#### From Previous Version

1. **Backup existing data**: Copy data directory
2. **Update configurations**: Add new memory settings to agent configs
3. **Install dependencies**: Run `pip install -r requirements_memory_enhanced.txt`
4. **Test migration**: Run enhanced test suite to verify functionality

### Web Search Integration (SearxNG)

Research agents can now perform comprehensive web searches:

```bash
# Set up SearxNG for web search
docker run -d -p 8080:8080 searxng/searxng

# Configure in .env
SEARXNG_URL=http://localhost:8080
```

### Available Tools

- **http_get**: HTTP requests and API calls
- **searxng_search**: Privacy-focused web search
- **math_eval**: Mathematical calculations and data analysis

### LLM Provider Integration

Agents use Ollama with the gemma3:270m model by default:

```bash
# Install and setup Ollama
curl -fsSL https://ollama.ai/install.sh | sh
ollama pull gemma3:270m
ollama serve
```

## 🔧 API Usage

### Agent Management

#### Create an Agent
```bash
curl -X POST "http://localhost:8000/agents/create" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_type": "customer_support",
    "name": "support_agent_1",
    "config": {
      "escalation_threshold": 0.4,
      "max_resolution_attempts": 5
    }
  }'
```

#### List All Agents
```bash
curl -X GET "http://localhost:8000/agents/"
```

#### Get Agent Information
```bash
curl -X GET "http://localhost:8000/agents/{agent_id}"
```

#### Chat with an Agent
```bash
curl -X POST "http://localhost:8000/agents/{agent_id}/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "I need help with a refund",
    "context": {
      "customer_id": "cust_123",
      "order_id": "order_456"
    }
  }'
```

#### Update Agent Configuration
```bash
curl -X PUT "http://localhost:8000/agents/{agent_id}/config" \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      "escalation_threshold": 0.5
    },
    "merge": true
  }'
```

#### Health Check
```bash
curl -X GET "http://localhost:8000/agents/{agent_id}/health"
```

#### Delete an Agent
```bash
curl -X DELETE "http://localhost:8000/agents/{agent_id}"
```

### Bulk Operations

#### Bulk Create Agents
```bash
curl -X POST "http://localhost:8000/agents/bulk-create" \
  -H "Content-Type: application/json" \
  -d '{
    "agents": [
      {
        "agent_type": "customer_support",
        "name": "support_1"
      },
      {
        "agent_type": "sales",
        "name": "sales_1",
        "config": {
          "qualification_threshold": 0.8
        }
      }
    ]
  }'
```

### Multi-Agent Collaboration

#### Coordinate Multiple Agents
```bash
curl -X POST "http://localhost:8000/agents/multi-agent/collaborate" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Help a customer who wants to buy our premium product",
    "agents": ["support_agent_id", "sales_agent_id"],
    "collaboration_mode": "sequential"
  }'
```

## 🤖 Agent Types

### Customer Support Agent
Specialized for customer service operations with advanced sentiment analysis and escalation handling.

**Capabilities:**
- Ticket creation and management
- Sentiment analysis and emotional intelligence
- Knowledge base search and retrieval
- Automatic escalation based on complexity and sentiment
- Multi-language support
- Resolution tracking and follow-up

**Default Configuration:**
```yaml
escalation_threshold: 0.3
max_resolution_attempts: 3
supported_languages: ["en"]
ticket_priority_mapping:
  high: ["urgent", "critical", "emergency"]
  medium: ["important", "refund", "billing"]
  low: ["general", "information", "question"]
```

**Example Usage:**
```python
from app.factory.agent_factory import AgentFactory

# Create customer support agent
agent = AgentFactory.create_agent(
    agent_type="customer_support",
    config={
        "escalation_threshold": 0.4,
        "supported_languages": ["en", "es"],
        "max_resolution_attempts": 5
    }
)

# Process customer inquiry
result = agent.process(
    query="I'm very frustrated with my recent order",
    context={
        "customer_id": "cust_123",
        "order_id": "order_456",
        "previous_interactions": 2
    }
)
```

### Sales Agent
Optimized for sales operations with lead qualification and deal management capabilities.

**Capabilities:**
- Lead qualification and scoring
- Product recommendation engine
- Pricing negotiation and discount management
- Deal pipeline tracking
- Customer profiling and segmentation
- Sales stage progression

**Default Configuration:**
```yaml
qualification_threshold: 0.7
max_discount: 0.15
product_catalog: []
sales_stages: ["lead", "qualified", "proposal", "negotiation", "closed"]
```

**Example Usage:**
```python
# Create sales agent
agent = AgentFactory.create_agent(
    agent_type="sales",
    config={
        "qualification_threshold": 0.8,
        "max_discount": 0.2,
        "product_catalog": ["premium", "standard", "basic"]
    }
)

# Process sales inquiry
result = agent.process(
    query="What are your pricing options for enterprise customers?",
    context={
        "customer_id": "enterprise_123",
        "budget": 50000,
        "decision_maker": True,
        "company_size": "large"
    }
)
```

### Research Agent
Designed for comprehensive research and information gathering with fact verification.

**Capabilities:**
- Multi-source information gathering
- Data analysis and synthesis
- Fact verification and confidence scoring
- Citation management and source tracking
- Report generation with structured findings
- Trend analysis and pattern recognition

**Default Configuration:**
```yaml
max_sources: 10
fact_check_threshold: 0.8
research_depth: "standard"
citation_style: "apa"
confidence_threshold: 0.7
```

**Example Usage:**
```python
# Create research agent
agent = AgentFactory.create_agent(
    agent_type="research",
    config={
        "max_sources": 15,
        "research_depth": "comprehensive",
        "citation_style": "mla"
    }
)

# Process research query
result = agent.process(
    query="What are the latest trends in artificial intelligence?",
    context={
        "research_type": "trend_analysis",
        "time_frame": "last_6_months",
        "depth": "comprehensive"
    }
)
```

## 🛠️ Development

### Running Tests
```bash
# Run all tests
pytest

# Run specific test categories
pytest tests/unit/                    # Unit tests
pytest tests/integration/             # Integration tests

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test file
pytest tests/unit/test_agents.py
```

### Code Quality
```bash
# Format code
make fmt

# Lint code
make lint

# Type checking
mypy app/
```

### Development Setup
```bash
# Install development dependencies
pip install -e ".[dev]"

# Pre-commit hooks
pre-commit install

# Run development server with auto-reload
uvicorn app.api.main:app --reload --host 0.0.0.0 --port 8000
```

## 📚 Examples

### Basic Agent Creation and Usage
```python
from app.factory.agent_factory import AgentFactory

# Create a customer support agent
support_agent = AgentFactory.create_agent("customer_support")

# Process a customer inquiry
result = support_agent.process(
    query="I can't access my account",
    context={"customer_id": "cust_123"}
)

print(f"Response: {result['response']}")
print(f"Ticket ID: {result['ticket']['ticket_id']}")
print(f"Escalation needed: {result['escalation_required']}")
```

### Multi-Agent Workflow
```python
from app.factory.agent_factory import AgentFactory
from app.orchestrators.multi_agent import MultiAgentOrchestrator

# Create multiple agents
support_agent = AgentFactory.create_agent("customer_support")
sales_agent = AgentFactory.create_agent("sales")
research_agent = AgentFactory.create_agent("research")

# Create orchestrator
orchestrator = MultiAgentOrchestrator([support_agent, sales_agent, research_agent])

# Process complex query requiring multiple agents
result = await orchestrator.process_collaborative(
    "A customer wants to upgrade their plan but has billing issues"
)
```

### Custom Agent Configuration
```python
# Create agent with custom configuration
custom_config = {
    "escalation_threshold": 0.2,  # Lower threshold for faster escalation
    "max_resolution_attempts": 7,
    "supported_languages": ["en", "es", "fr"],
    "custom_tools": ["knowledge_base", "crm_integration"],
    "notification_settings": {
        "email_alerts": True,
        "slack_integration": True
    }
}

agent = AgentFactory.create_agent(
    agent_type="customer_support",
    config=custom_config
)
```

### Using Configuration Files
```python
# Load agent from configuration file
agent = AgentFactory.create_agent(
    agent_type="sales",
    config_path="config/sales_enterprise.yaml"
)

# Save configuration template
AgentFactory.save_config_template(
    agent_type="research",
    output_path="config/research_template.yaml"
)
```

## 🔍 Monitoring and Observability

### Health Checks
All agents provide health check endpoints for monitoring:

```python
# Check agent health
health = agent.health_check()
print(f"Status: {health['status']}")
print(f"Uptime: {health['uptime']} seconds")
print(f"Capabilities: {health['capabilities_count']}")
```

### Logging
Structured logging with context tracking:

```python
import logging
from app.observability.logging import setup_logging

# Configure logging
setup_logging(level="INFO")

# Logs include context information
logger = logging.getLogger(__name__)
logger.info("Agent processing started", extra={
    "agent_id": "agent_123",
    "query_type": "customer_inquiry",
    "context": {"customer_id": "cust_456"}
})
```

### Metrics and Performance
Monitor agent performance and usage:

```python
# Agent execution metrics are automatically tracked
result = agent.process(query, context)

# Access performance metrics
print(f"Processing time: {result['metadata']['processing_time']}")
print(f"Token usage: {result['metadata']['token_usage']}")
print(f"Tools used: {result['metadata']['tools_used']}")
```

## 🚀 Deployment

### Docker Deployment
```bash
# Build Docker image
docker build -t agentkit .

# Run container
docker run -p 8000:8000 --env-file .env agentkit

# Using Docker Compose
docker-compose up -d
```

### Production Configuration
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  agentkit:
    build: .
    ports:
      - "8000:8000"
    environment:
      - LLM_PROVIDER=openai
      - LOG_LEVEL=INFO
      - MAX_WORKERS=8
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    restart: unless-stopped
```

### Environment-Specific Settings
```bash
# Production
export LLM_PROVIDER=openai
export LOG_LEVEL=WARNING
export MAX_WORKERS=16

# Development
export LLM_PROVIDER=ollama
export LOG_LEVEL=DEBUG
export MAX_WORKERS=4
```

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Add tests for new functionality
5. Run the test suite: `pytest`
6. Commit your changes: `git commit -m 'Add amazing feature'`
7. Push to the branch: `git push origin feature/amazing-feature`
8. Open a Pull Request

### Code Standards
- Follow PEP 8 style guidelines
- Add type hints to all functions
- Write comprehensive docstrings
- Maintain test coverage above 90%
- Update documentation for new features

### Testing Requirements
- Unit tests for all new modules
- Integration tests for API endpoints
- Performance tests for critical paths
- Error handling tests for edge cases

## 🔧 Recent Fixes & Improvements

### Qdrant Vector Database Integration (Latest)
- **Fixed Qdrant Initialization Error**: Resolved `NotImplementedError` when initializing Qdrant collections
- **HTTP Fallback Implementation**: Added comprehensive HTTP fallback methods for all Qdrant operations when client methods are not implemented
- **Production-Ready Error Handling**: Implemented robust error handling with detailed logging for all vector database operations
- **UUID Point ID Support**: Added automatic conversion of string keys to deterministic UUIDs for Qdrant point IDs
- **Comprehensive Testing**: Updated unit tests and integration tests for VectorDB adapter functionality
- **Memory System Enhancement**: Enhanced semantic memory with reliable vector database persistence

### Key Technical Improvements:
- **VectorDBAdapter**: Complete rewrite with HTTP fallback pattern for maximum compatibility
- **Collection Management**: Automatic collection creation and management with proper vector configuration
- **Vector Operations**: Support for add, search, clear, and collection info operations via both client and HTTP methods
- **API Integration**: Seamless integration with agent creation API for VectorDB-backed semantic memory
- **Test Coverage**: 100% test coverage for VectorDB functionality with comprehensive mocking

### Verified Functionality:
- ✅ Agent creation with VectorDB memory configuration
- ✅ Vector storage and retrieval operations
- ✅ HTTP fallback when Qdrant client methods are not implemented
- ✅ Collection lifecycle management (create, clear, delete)
- ✅ Error handling and logging for all failure scenarios
- ✅ Integration with existing memory system architecture

## 📄 License


## 🙏 Acknowledgments

- Built with FastAPI for high-performance API development
- Powered by Pydantic for data validation and settings management
- Supports multiple LLM providers for flexibility
- Inspired by modern agentic AI architectures and best practices

## 📞 Support

- **Documentation**: [API Docs](http://localhost:8000/docs)
- **Issues**: [GitHub Issues](https://github.com/your-repo/agentkit/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-repo/agentkit/discussions)

---

**AgentKit** - Building the future of agentic AI, one agent at a time. 🤖✨
