"""
Main FastAPI application for AgentKit.

This module sets up the FastAPI application with:
- Centralized settings management
- Enhanced logging and exception handling
- CORS middleware configuration
- Request/response logging middleware
- Health check endpoints
- API documentation

The application uses the settings system for all configuration
and provides comprehensive error handling and observability.
"""

from fastapi import Fast<PERSON><PERSON>, APIRouter
from fastapi.middleware.cors import CORSMiddleware

from app.core.settings import get_settings
from app.core.exceptions import setup_exception_handlers
from app.observability.logging import setup_logging, LoggingMiddleware, get_logger
from app.database import init_database
from app.api.routes import agents, memory, chat_history, organizations, agent_types, tools

# Initialize logging first
setup_logging()
logger = get_logger(__name__)

# Get settings
settings = get_settings()

# Initialize database
try:
    init_database()
    logger.info("Database initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize database: {e}")
    # Continue startup - database issues shouldn't prevent API from starting

# Create FastAPI app with settings-based configuration
app = FastAPI(
    title=settings.app_name,
    description="Configurable Agents with Planning, Tools, MCP, Memory, Validation, Policies, and Observability",
    version=settings.app_version,
    debug=settings.debug,
    docs_url=settings.api.docs_url if settings.api.docs_enabled else None,
    redoc_url=settings.api.redoc_url if settings.api.docs_enabled else None,
)

# Setup exception handlers
setup_exception_handlers(app)

# Add logging middleware
app.add_middleware(LoggingMiddleware)

# Enable CORS with settings-based configuration
if settings.api.cors_enabled:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.api.cors_origins,
        allow_credentials=True,
        allow_methods=settings.api.cors_methods,
        allow_headers=settings.api.cors_headers,
    )
    logger.info("CORS middleware enabled", extra={
        "origins": settings.api.cors_origins,
        "methods": settings.api.cors_methods
    })

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "app_name": settings.app_name,
        "version": settings.app_version,
        "environment": settings.environment
    }

# Include agents router directly (it already has /agents prefix)
app.include_router(agents.router, tags=["Agents"])

# Include memory and chat history routers
app.include_router(memory.router, tags=["Memory"])
app.include_router(chat_history.router, tags=["Chat History"])

# Include new multi-tenant routers
app.include_router(organizations.router, tags=["Organizations"])
app.include_router(agent_types.router, tags=["Agent Types"])

# Include tools router
app.include_router(tools.router, tags=["Tools"])

# Note: Removed duplicate agent-factory routes to fix OpenAPI schema generation
# The agents routes are already included above without prefix

# Log application startup
logger.info("AgentKit API application initialized", extra={
    "app_name": settings.app_name,
    "version": settings.app_version,
    "environment": settings.environment,
    "debug": settings.debug,
    "llm_provider": settings.llm.provider
})


