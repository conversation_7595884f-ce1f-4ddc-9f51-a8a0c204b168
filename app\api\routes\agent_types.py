"""
Agent Types management API routes.

This module provides REST API endpoints for managing agent types
in the multi-tenant AgentKit system.
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query, Header
from sqlalchemy.orm import Session

from ...database import get_db_session, AgentTypeRepository, OrganizationRepository
from ...database.models import AgentType
from ..schemas import (
    AgentTypeCreate, AgentTypeUpdate, AgentTypeResponse,
    AgentTypeListResponse, AgentTypeWithRelationsResponse
)
from ..utils import setup_request_context
from ...observability.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/agent-types", tags=["Agent Types"])


def get_organization_context(
    organization_id: int = Header(..., alias="X-Organization-ID"),
    db: Session = Depends(get_db_session)
) -> int:
    """
    Get and validate organization context from headers.
    
    Args:
        organization_id: Organization ID from header
        db: Database session
        
    Returns:
        Validated organization ID
        
    Raises:
        HTTPException: If organization not found or inactive
    """
    org_repo = OrganizationRepository(db)
    organization = org_repo.get_by_id(organization_id)
    
    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Organization with ID {organization_id} not found"
        )
    
    if not organization.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Organization is not active"
        )
    
    return organization_id


@router.get("/", response_model=AgentTypeListResponse)
async def list_agent_types(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    category: str = Query(None, description="Filter by category"),
    organization_id: int = Depends(get_organization_context),
    db: Session = Depends(get_db_session)
):
    """
    List agent types for an organization with pagination.
    
    Args:
        skip: Number of records to skip for pagination
        limit: Maximum number of records to return
        category: Optional category filter
        organization_id: Organization ID from context
        db: Database session
        
    Returns:
        Paginated list of agent types
    """
    try:
        repo = AgentTypeRepository(db)

        # Get total count for pagination
        total = repo.count_by_organization(organization_id, category)

        if category:
            agent_types = repo.get_by_category(category, organization_id)
            # Apply pagination manually for category filter
            agent_types = agent_types[skip:skip + limit]
        else:
            agent_types = repo.get_by_organization(organization_id, skip=skip, limit=limit)

        # Calculate pagination info
        pages = (total + limit - 1) // limit

        return AgentTypeListResponse(
            items=[AgentTypeResponse.model_validate(at) for at in agent_types],
            total=total,
            page=(skip // limit) + 1,
            size=limit,
            pages=pages
        )
    except Exception as e:
        logger.error(f"Failed to list agent types for org {organization_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve agent types"
        )


@router.get("/active", response_model=List[AgentTypeResponse])
async def list_active_agent_types(
    organization_id: int = Depends(get_organization_context),
    db: Session = Depends(get_db_session)
):
    """
    List all active agent types for an organization.

    Args:
        organization_id: Organization ID from context
        db: Database session

    Returns:
        List of active agent types
    """
    try:
        repo = AgentTypeRepository(db)
        agent_types = repo.get_active_types(organization_id)

        return [AgentTypeResponse.model_validate(at) for at in agent_types]
    except Exception as e:
        logger.error(f"Failed to list active agent types for org {organization_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve active agent types"
        )


@router.get("/categories", response_model=List[str])
async def list_agent_type_categories(
    organization_id: int = Depends(get_organization_context),
    db: Session = Depends(get_db_session)
):
    """
    List all unique categories for agent types in an organization.

    Args:
        organization_id: Organization ID from context
        db: Database session

    Returns:
        List of unique categories
    """
    try:
        # Get distinct categories for the organization
        categories = db.query(AgentType.category).filter(
            AgentType.organization_id == organization_id,
            AgentType.category.isnot(None),
            AgentType.is_active == True
        ).distinct().all()

        return [cat[0] for cat in categories if cat[0]]
    except Exception as e:
        logger.error(f"Failed to list agent type categories for org {organization_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve agent type categories"
        )


@router.get("/{agent_type_id}", response_model=AgentTypeWithRelationsResponse)
async def get_agent_type(
    agent_type_id: int,
    organization_id: int = Depends(get_organization_context),
    db: Session = Depends(get_db_session)
):
    """
    Get a specific agent type by ID with related resources.
    
    Args:
        agent_type_id: Agent type ID
        organization_id: Organization ID from context
        db: Database session
        
    Returns:
        Agent type details with related tools, memory configs, and knowledge bases
        
    Raises:
        HTTPException: If agent type not found or not in organization
    """
    try:
        repo = AgentTypeRepository(db)
        agent_type = repo.get_by_id(agent_type_id)
        
        if not agent_type:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent type with ID {agent_type_id} not found"
            )
        
        # Verify agent type belongs to the organization
        if agent_type.organization_id != organization_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Agent type does not belong to the specified organization"
            )
        
        # Convert to response model with relationships
        response_data = AgentTypeResponse.model_validate(agent_type).model_dump()
        
        # Add related resources (tools, memory configs, knowledge bases)
        # Note: These would be populated from the relationships in a real implementation
        response_data.update({
            "tools": [],
            "memory_configs": [],
            "knowledge_bases": []
        })
        
        return AgentTypeWithRelationsResponse(**response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get agent type {agent_type_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve agent type"
        )


@router.post("/", response_model=AgentTypeResponse, status_code=status.HTTP_201_CREATED)
async def create_agent_type(
    agent_type_data: AgentTypeCreate,
    organization_id: int = Depends(get_organization_context),
    db: Session = Depends(get_db_session)
):
    """
    Create a new agent type.
    
    Args:
        agent_type_data: Agent type creation data
        organization_id: Organization ID from context
        db: Database session
        
    Returns:
        Created agent type details
        
    Raises:
        HTTPException: If agent type name already exists in organization
    """
    try:
        repo = AgentTypeRepository(db)
        
        # Ensure organization_id matches context
        if agent_type_data.organization_id != organization_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Organization ID in request body must match header"
            )
        
        # Check if agent type name already exists in organization
        existing_type = repo.get_by_name(agent_type_data.name, organization_id)
        if existing_type:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"Agent type with name '{agent_type_data.name}' already exists in organization"
            )
        
        # Create the agent type
        agent_type = repo.create(agent_type_data.model_dump())
        
        logger.info(f"Created agent type: {agent_type.name} (ID: {agent_type.id}) for org {organization_id}")
        return AgentTypeResponse.model_validate(agent_type)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create agent type: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create agent type"
        )


@router.put("/{agent_type_id}", response_model=AgentTypeResponse)
async def update_agent_type(
    agent_type_id: int,
    agent_type_data: AgentTypeUpdate,
    organization_id: int = Depends(get_organization_context),
    db: Session = Depends(get_db_session)
):
    """
    Update an existing agent type.
    
    Args:
        agent_type_id: Agent type ID
        agent_type_data: Agent type update data
        organization_id: Organization ID from context
        db: Database session
        
    Returns:
        Updated agent type details
        
    Raises:
        HTTPException: If agent type not found or name conflict
    """
    try:
        repo = AgentTypeRepository(db)
        
        # Check if agent type exists and belongs to organization
        existing_type = repo.get_by_id(agent_type_id)
        if not existing_type:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent type with ID {agent_type_id} not found"
            )
        
        if existing_type.organization_id != organization_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Agent type does not belong to the specified organization"
            )
        
        # Check for name conflicts if name is being updated
        if agent_type_data.name and agent_type_data.name != existing_type.name:
            name_conflict = repo.get_by_name(agent_type_data.name, organization_id)
            if name_conflict:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Agent type with name '{agent_type_data.name}' already exists in organization"
                )
        
        # Update the agent type
        update_data = agent_type_data.model_dump(exclude_unset=True)
        agent_type = repo.update(agent_type_id, update_data)
        
        logger.info(f"Updated agent type: {agent_type.name} (ID: {agent_type.id})")
        return AgentTypeResponse.model_validate(agent_type)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update agent type {agent_type_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update agent type"
        )


@router.delete("/{agent_type_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_agent_type(
    agent_type_id: int,
    organization_id: int = Depends(get_organization_context),
    db: Session = Depends(get_db_session)
):
    """
    Delete an agent type.

    Args:
        agent_type_id: Agent type ID
        organization_id: Organization ID from context
        db: Database session

    Raises:
        HTTPException: If agent type not found or not in organization
    """
    try:
        repo = AgentTypeRepository(db)

        # Check if agent type exists and belongs to organization
        agent_type = repo.get_by_id(agent_type_id)
        if not agent_type:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent type with ID {agent_type_id} not found"
            )

        if agent_type.organization_id != organization_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Agent type does not belong to the specified organization"
            )

        # Delete the agent type
        success = repo.delete(agent_type_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete agent type"
            )

        logger.info(f"Deleted agent type: {agent_type.name} (ID: {agent_type_id})")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete agent type {agent_type_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete agent type"
        )



