"""
Organization management API routes.

This module provides REST API endpoints for managing organizations
in the multi-tenant AgentKit system.
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from ...database import get_db_session, OrganizationRepository
from ...database.models import Organization
from ..schemas import (
    OrganizationCreate, OrganizationUpdate, OrganizationResponse,
    OrganizationListResponse, PaginatedResponse
)
from ..utils import setup_request_context
from ...observability.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/organizations", tags=["Organizations"])


@router.get("/", response_model=OrganizationListResponse)
async def list_organizations(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_db_session)
):
    """
    List all organizations with pagination.
    
    Args:
        skip: Number of records to skip for pagination
        limit: Maximum number of records to return
        db: Database session
        
    Returns:
        Paginated list of organizations
    """
    try:
        repo = OrganizationRepository(db)
        organizations = repo.get_all(skip=skip, limit=limit)
        
        # Get total count for pagination
        total = db.query(Organization).count()
        pages = (total + limit - 1) // limit
        
        return OrganizationListResponse(
            items=[OrganizationResponse.model_validate(org) for org in organizations],
            total=total,
            page=(skip // limit) + 1,
            size=limit,
            pages=pages
        )
    except Exception as e:
        logger.error(f"Failed to list organizations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve organizations"
        )


@router.get("/{organization_id}", response_model=OrganizationResponse)
async def get_organization(
    organization_id: int,
    db: Session = Depends(get_db_session)
):
    """
    Get a specific organization by ID.
    
    Args:
        organization_id: Organization ID
        db: Database session
        
    Returns:
        Organization details
        
    Raises:
        HTTPException: If organization not found
    """
    try:
        repo = OrganizationRepository(db)
        organization = repo.get_by_id(organization_id)
        
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Organization with ID {organization_id} not found"
            )
        
        return OrganizationResponse.model_validate(organization)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get organization {organization_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve organization"
        )


@router.post("/", response_model=OrganizationResponse, status_code=status.HTTP_201_CREATED)
async def create_organization(
    organization_data: OrganizationCreate,
    db: Session = Depends(get_db_session)
):
    """
    Create a new organization.
    
    Args:
        organization_data: Organization creation data
        db: Database session
        
    Returns:
        Created organization details
        
    Raises:
        HTTPException: If organization name already exists
    """
    try:
        repo = OrganizationRepository(db)
        
        # Check if organization name already exists
        existing_org = repo.get_by_name(organization_data.name)
        if existing_org:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"Organization with name '{organization_data.name}' already exists"
            )
        
        # Create the organization
        organization = repo.create(organization_data.model_dump())
        
        logger.info(f"Created organization: {organization.name} (ID: {organization.id})")
        return OrganizationResponse.model_validate(organization)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create organization: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create organization"
        )


@router.put("/{organization_id}", response_model=OrganizationResponse)
async def update_organization(
    organization_id: int,
    organization_data: OrganizationUpdate,
    db: Session = Depends(get_db_session)
):
    """
    Update an existing organization.
    
    Args:
        organization_id: Organization ID
        organization_data: Organization update data
        db: Database session
        
    Returns:
        Updated organization details
        
    Raises:
        HTTPException: If organization not found or name conflict
    """
    try:
        repo = OrganizationRepository(db)
        
        # Check if organization exists
        existing_org = repo.get_by_id(organization_id)
        if not existing_org:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Organization with ID {organization_id} not found"
            )
        
        # Check for name conflicts if name is being updated
        if organization_data.name and organization_data.name != existing_org.name:
            name_conflict = repo.get_by_name(organization_data.name)
            if name_conflict:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Organization with name '{organization_data.name}' already exists"
                )
        
        # Update the organization
        update_data = organization_data.model_dump(exclude_unset=True)
        organization = repo.update(organization_id, update_data)
        
        logger.info(f"Updated organization: {organization.name} (ID: {organization.id})")
        return OrganizationResponse.model_validate(organization)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update organization {organization_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update organization"
        )


@router.delete("/{organization_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_organization(
    organization_id: int,
    db: Session = Depends(get_db_session)
):
    """
    Delete an organization.
    
    Args:
        organization_id: Organization ID
        db: Database session
        
    Raises:
        HTTPException: If organization not found
    """
    try:
        repo = OrganizationRepository(db)
        
        # Check if organization exists
        organization = repo.get_by_id(organization_id)
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Organization with ID {organization_id} not found"
            )
        
        # Delete the organization
        success = repo.delete(organization_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete organization"
            )
        
        logger.info(f"Deleted organization: {organization.name} (ID: {organization_id})")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete organization {organization_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete organization"
        )


@router.get("/active", response_model=List[OrganizationResponse])
async def list_active_organizations(
    db: Session = Depends(get_db_session)
):
    """
    List all active organizations.
    
    Args:
        db: Database session
        
    Returns:
        List of active organizations
    """
    try:
        repo = OrganizationRepository(db)
        organizations = repo.get_active_organizations()
        
        return [OrganizationResponse.model_validate(org) for org in organizations]
    except Exception as e:
        logger.error(f"Failed to list active organizations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve active organizations"
        )
