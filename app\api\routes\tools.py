"""
Tools Management API Routes

Provides endpoints for managing, testing, and creating tools.
"""

import inspect
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field
from datetime import datetime
import json
import traceback
from loguru import logger

from app.tools.registry import ToolRegistry
from app.tools.base import Tool, ToolSpec
from app.tools.dynamic_loader import get_tool_loader
from app.database.repositories import ToolRegistryRepository, ToolExecutionHistoryRepository
from app.core.dependencies import get_tool_repository, get_tool_execution_history_repository

async def _invoke_tool_callable(callable_obj, *args, **kwargs):
    """Invoke a tool callable and await it when necessary."""
    if inspect.iscoroutinefunction(callable_obj):
        return await callable_obj(*args, **kwargs)
    result = callable_obj(*args, **kwargs)
    if inspect.isawaitable(result):
        return await result
    return result

# Database-driven tool registry service
class DatabaseToolRegistryService:
    """Service to manage tools from database and runtime registry."""

    def __init__(self, tool_repo: ToolRegistryRepository):
        self.tool_repo = tool_repo
        self._runtime_registry = None

    def get_runtime_registry(self):
        """Get or create runtime registry with tools from database."""
        if self._runtime_registry is None:
            from app.tools.registry import ToolRegistry
            self._runtime_registry = ToolRegistry()
            self._load_tools_from_database()
        return self._runtime_registry

    def _load_tools_from_database(self):
        """Load tools from database into runtime registry."""
        try:
            # Get all active tools from database
            db_tools = self.tool_repo.get_global_tools()  # This filters by is_active=True

            # Import available tool implementations
            from app.tools.http_get import http_get
            from app.tools.math_eval import math_eval
            from app.tools.searxng_search import searxng_search
            from app.tools.duckduckgo_search import duckduckgo_search
            from app.tools.markdown_to_pdf import markdown_to_pdf
            from app.tools.web_content_parser import web_content_parser

            # Map of available tool implementations
            available_tools = {
                "http_get": http_get,
                "math_eval": math_eval,
                "searxng_search": searxng_search,
                "duckduckgo_search": duckduckgo_search,
                "markdown_to_pdf": markdown_to_pdf,
                "web_content_parser": web_content_parser
            }

            # Register tools that exist in database and have implementations
            for db_tool in db_tools:
                if db_tool.name in available_tools:
                    tool_impl = available_tools[db_tool.name]
                    if tool_impl is not None:
                        # Register tool with database name, not internal name
                        self._runtime_registry._tools[db_tool.name] = tool_impl
                        logger.info(f"Registered tool: {db_tool.name}")
                        logger.info(f"Loaded tool from database: {db_tool.name}")

            logger.info(f"Loaded {self._runtime_registry.count()} tools from database")

        except Exception as e:
            logger.error(f"Failed to load tools from database: {str(e)}")
            # Fallback to default tools
            self._load_default_tools()

    def _load_default_tools(self):
        """Load default tools as fallback."""
        try:
            from app.tools.http_get import http_get
            from app.tools.math_eval import math_eval
            from app.tools.searxng_search import searxng_search
            from app.tools.duckduckgo_search import duckduckgo_search
            from app.tools.markdown_to_pdf import markdown_to_pdf

            default_tools = [http_get, math_eval, searxng_search, duckduckgo_search, markdown_to_pdf]

            for tool in default_tools:
                if tool is not None:
                    self._runtime_registry.register(tool)
                    logger.info(f"Loaded default tool: {tool.spec.name}")

        except Exception as e:
            logger.error(f"Failed to load default tools: {str(e)}")

# Global registry service instance
_registry_service = None

def get_registry_service(tool_repo: ToolRegistryRepository = None) -> DatabaseToolRegistryService:
    """Get or create the global registry service."""
    global _registry_service
    if _registry_service is None and tool_repo is not None:
        _registry_service = DatabaseToolRegistryService(tool_repo)
    return _registry_service

def get_registry(tool_repo: ToolRegistryRepository = None):
    """Get the runtime registry from the database-driven service."""
    try:
        if tool_repo is not None:
            service = get_registry_service(tool_repo)
            if service is not None:
                return service.get_runtime_registry()
    except Exception as e:
        logger.warning(f"Failed to get database-driven registry: {str(e)}")

    # Fallback to agents registry
    try:
        from app.api.routes.agents import registry
        if registry is not None:
            return registry
        else:
            logger.warning("Agents registry is None")
    except (ImportError, AttributeError) as e:
        logger.error(f"Failed to get registry from agents module: {str(e)}")

    # Last resort: create a minimal registry
    try:
        from app.tools.registry import ToolRegistry
        fallback_registry = ToolRegistry()
        logger.warning("Using fallback empty registry")
        return fallback_registry
    except Exception as e:
        logger.error(f"Failed to create fallback registry: {str(e)}")
        return None

def sync_default_tools_to_database(tool_repo: ToolRegistryRepository):
    """Sync default tools to database if they don't exist."""
    try:
        from app.tools.http_get import http_get
        from app.tools.math_eval import math_eval
        from app.tools.searxng_search import searxng_search
        from app.tools.duckduckgo_search import duckduckgo_search
        from app.tools.markdown_to_pdf import markdown_to_pdf

        default_tools = [
            (http_get, "HTTP GET", "Make HTTP GET requests to web APIs"),
            (math_eval, "Math Evaluator", "Evaluate mathematical expressions safely"),
            (searxng_search, "SearxNG Search", "Search the web using SearxNG"),
            (duckduckgo_search, "DuckDuckGo Search", "Search the web using DuckDuckGo"),
            (markdown_to_pdf, "Markdown to PDF", "Convert Markdown documents to PDF")
        ]

        for tool_impl, display_name, description in default_tools:
            if tool_impl is not None:
                # Check if tool already exists in database
                existing_tool = tool_repo.get_by_name(tool_impl.spec.name)
                if not existing_tool:
                    # Create tool in database
                    tool_data = {
                        "name": tool_impl.spec.name,
                        "display_name": display_name,
                        "description": description,
                        "category": "default",
                        "version": "1.0.0",
                        "status": "active",
                        "configuration": {
                            "spec": {
                                "name": tool_impl.spec.name,
                                "description": tool_impl.spec.description,
                                "parameters": tool_impl.spec.parameters
                            }
                        },
                        "is_system": True
                    }

                    tool_repo.create(tool_data)
                    logger.info(f"Synced default tool to database: {tool_impl.spec.name}")
                else:
                    logger.debug(f"Tool already exists in database: {tool_impl.spec.name}")

        logger.info("Default tools sync completed")

    except Exception as e:
        logger.error(f"Failed to sync default tools to database: {str(e)}")
        raise

router = APIRouter(prefix="/api/tools", tags=["tools"])

# Request/Response Models
class ToolSpecResponse(BaseModel):
    name: str
    description: str
    input_schema: Dict[str, Any]
    output_schema: Dict[str, Any]
    category: Optional[str] = None
    version: Optional[str] = None
    author: Optional[str] = None

class ToolResponse(BaseModel):
    id: str
    spec: ToolSpecResponse
    status: str = "active"
    last_used: Optional[datetime] = None
    usage_count: int = 0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class ExecuteToolRequest(BaseModel):
    tool_name: str
    parameters: Dict[str, Any]

class ToolExecutionResponse(BaseModel):
    id: str
    tool_name: str
    input: Dict[str, Any]
    output: Dict[str, Any]
    success: bool
    execution_time: float
    timestamp: datetime
    error: Optional[str] = None

class CreateToolRequest(BaseModel):
    name: str = Field(..., description="Tool name")
    description: str = Field(..., description="Tool description")
    input_schema: Dict[str, Any] = Field(..., description="Input schema")
    output_schema: Dict[str, Any] = Field(..., description="Output schema")
    category: Optional[str] = Field(None, description="Tool category")
    module_path: Optional[str] = Field(None, description="Python module path")
    class_name: Optional[str] = Field(None, description="Tool class name")
    version: Optional[str] = Field(None, description="Tool version")
    author: Optional[str] = Field(None, description="Tool author")

class UpdateToolRequest(BaseModel):
    description: Optional[str] = Field(None, description="Tool description")
    input_schema: Optional[Dict[str, Any]] = Field(None, description="Input schema")
    output_schema: Optional[Dict[str, Any]] = Field(None, description="Output schema")
    category: Optional[str] = Field(None, description="Tool category")
    version: Optional[str] = Field(None, description="Tool version")
    author: Optional[str] = Field(None, description="Tool author")
    is_active: Optional[bool] = Field(None, description="Tool active status")

# Use registry from agents.py to avoid duplication

@router.get("/", response_model=List[ToolResponse])
async def get_tools(
    category: Optional[str] = None,
    status: Optional[str] = None,
    tool_repo: ToolRegistryRepository = Depends(get_tool_repository)
):
    """Get all registered tools with optional filtering."""
    try:
        # Get tools from database
        db_tools = tool_repo.get_all()

        # If no tools in database, sync default tools
        if not db_tools:
            try:
                sync_default_tools_to_database(tool_repo)
                db_tools = tool_repo.get_all()
            except Exception as e:
                logger.warning(f"Failed to sync default tools: {str(e)}")
                db_tools = []
        
        # Get registered tools from registry
        registry = get_registry(tool_repo)
        registered_tools = registry.list() if registry else []
        registered_names = {tool.spec.name for tool in registered_tools}
        
        tools = []
        for db_tool in db_tools:
            # Check if tool is currently registered
            tool_status = "active" if db_tool.name in registered_names else "inactive"
            
            # Apply filters
            if category and db_tool.category != category:
                continue
            if status and tool_status != status:
                continue
            
            tools.append(ToolResponse(
                id=str(db_tool.id),
                spec=ToolSpecResponse(
                    name=db_tool.name,
                    description=db_tool.description or "",
                    input_schema=db_tool.parameters_schema or {},
                    output_schema=db_tool.output_schema or {},
                    category=db_tool.category,
                    version=db_tool.version,
                    author=db_tool.author
                ),
                status=tool_status,
                usage_count=db_tool.usage_count or 0,
                last_used=db_tool.last_used,
                created_at=db_tool.created_at,
                updated_at=db_tool.updated_at
            ))
        
        logger.info(f"Retrieved {len(tools)} tools", extra={
            "category_filter": category,
            "status_filter": status
        })
        
        return tools
        
    except Exception as e:
        logger.error(f"Failed to get tools: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve tools")

@router.get("/{tool_name}", response_model=ToolResponse)
async def get_tool(
    tool_name: str,
    tool_repo: ToolRegistryRepository = Depends(get_tool_repository)
):
    """Get a specific tool by name."""
    try:
        # Get from database
        db_tool = tool_repo.get_by_name(tool_name)
        if not db_tool:
            raise HTTPException(status_code=404, detail=f"Tool '{tool_name}' not found")
        
        # Check if registered
        try:
            get_registry().get(tool_name)
            status = "active"
        except KeyError:
            status = "inactive"
        
        return ToolResponse(
            id=str(db_tool.id),
            spec=ToolSpecResponse(
                name=db_tool.name,
                description=db_tool.description,
                input_schema=db_tool.parameters_schema,
                output_schema=db_tool.output_schema or {},
                category=db_tool.category,
                version=db_tool.version
            ),
            status=status,
            usage_count=db_tool.usage_count or 0,
            created_at=db_tool.created_at,
            updated_at=db_tool.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get tool {tool_name}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve tool")

@router.post("/execute", response_model=ToolExecutionResponse)
async def execute_tool(
    request: ExecuteToolRequest,
    background_tasks: BackgroundTasks,
    tool_repo: ToolRegistryRepository = Depends(get_tool_repository),
    execution_repo: ToolExecutionHistoryRepository = Depends(get_tool_execution_history_repository)
):
    """Execute a tool with given parameters."""
    logger.info(f"Execute tool endpoint called with tool_name: {request.tool_name}")
    start_time = datetime.now()
    execution_id = f"exec_{int(start_time.timestamp() * 1000)}"

    try:
        # Get tool from registry
        registry = get_registry(tool_repo)
        logger.info(f"Registry object: {registry}")
        logger.info(f"Registry has {registry.count() if registry else 0} tools")
        if registry:
            available_tools = [tool.spec.name for tool in registry.list()]
            logger.info(f"Available tools: {available_tools}")
        else:
            logger.error("Registry is None!")

        logger.info(f"About to call registry.get() for tool: {request.tool_name}")
        logger.info(f"Registry object ID: {id(registry)}")
        logger.info(f"Registry._tools keys: {list(registry._tools.keys()) if registry and hasattr(registry, '_tools') else 'No _tools attribute'}")

        try:
            tool = registry.get(request.tool_name) if registry else None
            logger.info(f"Successfully got tool from registry: {tool}")
            logger.info(f"Tool type: {type(tool)}")
        except KeyError as e:
            logger.error(f"Tool lookup failed: {str(e)}")
            logger.error(f"Requested tool: '{request.tool_name}'")
            logger.error(f"Available tools in registry: {list(registry._tools.keys()) if registry else []}")
            raise HTTPException(status_code=404, detail=f"Tool '{request.tool_name}' not found")
        except Exception as e:
            logger.error(f"Unexpected error during tool lookup: {str(e)}")
            logger.error(f"Error type: {type(e)}")
            raise HTTPException(status_code=404, detail=f"Tool '{request.tool_name}' not found")

        if tool is None:
            logger.error(f"Tool is None after registry.get() call")
            raise HTTPException(status_code=404, detail=f"Tool '{request.tool_name}' not found")

        logger.info(f"Executing tool: {request.tool_name}", extra={
            "execution_id": execution_id,
            "parameters": request.parameters
        })

        # Execute tool
        try:
            if hasattr(tool, 'execute'):
                # For class-based tools with execute method
                logger.info(f"Calling tool.execute() with parameters: {request.parameters}")
                execute_method = getattr(tool, 'execute')
                result = await _invoke_tool_callable(execute_method, request.parameters)
            elif hasattr(tool, '__call__'):
                # For function-based tools or tools with __call__ method
                logger.info(f"Calling tool() with parameters: {request.parameters}")
                call_method = getattr(tool, '__call__')
                result = await _invoke_tool_callable(call_method, request.parameters)
            else:
                raise ValueError(f"Tool {request.tool_name} does not have execute method or __call__ method")
            logger.info(f"Tool execution successful, result: {result}")
        except Exception as e:
            logger.error(f"Tool execution failed with error: {str(e)}")
            logger.error(f"Error type: {type(e)}")
            logger.error(f"Tool: {tool}")
            logger.error(f"Parameters: {request.parameters}")
            # Re-raise the original exception instead of converting to "Tool not found"
            raise
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()

        # Update tool statistics in background
        background_tasks.add_task(
            _update_tool_stats,
            tool_repo,
            request.tool_name,
            True,  # success
            execution_time
        )

        # Create execution history record in background
        background_tasks.add_task(
            _create_execution_record,
            execution_repo,
            {
                "execution_id": execution_id,
                "tool_name": request.tool_name,
                "input_parameters": request.parameters,
                "output_result": result,
                "success": True,
                "execution_time": execution_time,
                "started_at": start_time,
                "completed_at": end_time
            }
        )
        
        response = ToolExecutionResponse(
            id=execution_id,
            tool_name=request.tool_name,
            input=request.parameters,
            output=result,
            success=True,
            execution_time=execution_time,
            timestamp=start_time
        )
        
        logger.info(f"Tool execution completed successfully", extra={
            "execution_id": execution_id,
            "execution_time": execution_time
        })
        
        return response

    except Exception as e:
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        error_msg = str(e)

        logger.error(f"Tool execution failed", extra={
            "execution_id": execution_id,
            "tool_name": request.tool_name,
            "error": error_msg,
            "traceback": traceback.format_exc()
        })

        # Update tool statistics for failed execution
        background_tasks.add_task(
            _update_tool_stats,
            tool_repo,
            request.tool_name,
            False,  # success = False
            execution_time
        )

        # Create execution history record for failed execution
        background_tasks.add_task(
            _create_execution_record,
            execution_repo,
            {
                "execution_id": execution_id,
                "tool_name": request.tool_name,
                "input_parameters": request.parameters,
                "output_result": {},
                "success": False,
                "error_message": error_msg,
                "execution_time": execution_time,
                "started_at": start_time,
                "completed_at": end_time
            }
        )

        return ToolExecutionResponse(
            id=execution_id,
            tool_name=request.tool_name,
            input=request.parameters,
            output={},
            success=False,
            execution_time=execution_time,
            timestamp=start_time,
            error=error_msg
        )

@router.post("/", response_model=ToolResponse)
async def create_tool(
    request: CreateToolRequest,
    tool_repo: ToolRegistryRepository = Depends(get_tool_repository)
):
    """Create a new tool."""
    try:
        # Check if tool already exists
        existing_tool = tool_repo.get_by_name(request.name)
        if existing_tool:
            raise HTTPException(status_code=400, detail=f"Tool '{request.name}' already exists")
        
        # Create tool in database
        tool_data = {
            "name": request.name,
            "description": request.description,
            "tool_type": "custom",
            "category": request.category or "general",
            "module_path": request.module_path or "app.tools.custom",
            "class_name": request.class_name or "CustomTool",
            "parameters_schema": request.input_schema,
            "output_schema": request.output_schema,
            "version": request.version,
            "author": request.author,
            "configuration": {}
        }
        
        db_tool = tool_repo.create(tool_data)
        
        logger.info(f"Created tool: {request.name}", extra={
            "tool_id": db_tool.id,
            "category": request.category
        })
        
        return ToolResponse(
            id=str(db_tool.id),
            spec=ToolSpecResponse(
                name=db_tool.name,
                description=db_tool.description,
                input_schema=db_tool.parameters_schema,
                output_schema=db_tool.output_schema or {},
                category=db_tool.category
            ),
            status="inactive",  # Not registered yet
            usage_count=0,
            created_at=db_tool.created_at,
            updated_at=db_tool.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create tool: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create tool")


@router.put("/{tool_name}", response_model=ToolResponse)
async def update_tool(
    tool_name: str,
    request: UpdateToolRequest,
    tool_repo: ToolRegistryRepository = Depends(get_tool_repository)
):
    """Update an existing tool."""
    try:
        # Get existing tool
        existing_tool = tool_repo.get_by_name(tool_name)
        if not existing_tool:
            raise HTTPException(status_code=404, detail=f"Tool '{tool_name}' not found")

        # Prepare update data
        update_data = {}
        if request.description is not None:
            update_data["description"] = request.description
        if request.input_schema is not None:
            update_data["parameters_schema"] = request.input_schema
        if request.output_schema is not None:
            update_data["output_schema"] = request.output_schema
        if request.category is not None:
            update_data["category"] = request.category
        if request.version is not None:
            update_data["version"] = request.version
        if request.author is not None:
            update_data["author"] = request.author
        if request.is_active is not None:
            update_data["is_active"] = request.is_active

        # Update tool in database
        updated_tool = tool_repo.update(existing_tool.id, update_data)
        if not updated_tool:
            raise HTTPException(status_code=500, detail="Failed to update tool")

        # Check if tool is registered
        try:
            get_registry().get(tool_name)
            status = "active"
        except KeyError:
            status = "inactive"

        logger.info(f"Updated tool: {tool_name}", extra={
            "tool_id": updated_tool.id,
            "updated_fields": list(update_data.keys())
        })

        return ToolResponse(
            id=str(updated_tool.id),
            spec=ToolSpecResponse(
                name=updated_tool.name,
                description=updated_tool.description or "",
                input_schema=updated_tool.parameters_schema or {},
                output_schema=updated_tool.output_schema or {},
                category=updated_tool.category,
                version=updated_tool.version,
                author=updated_tool.author
            ),
            status=status,
            usage_count=updated_tool.usage_count or 0,
            last_used=updated_tool.last_used,
            created_at=updated_tool.created_at,
            updated_at=updated_tool.updated_at
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update tool: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to update tool")


@router.get("/categories/", response_model=List[str])
async def get_tool_categories(
    tool_repo: ToolRegistryRepository = Depends(get_tool_repository)
):
    """Get all tool categories."""
    try:
        categories = tool_repo.get_categories()
        return sorted(categories)
    except Exception as e:
        logger.error(f"Failed to get tool categories: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve categories")


@router.get("/{tool_name}/executions", response_model=List[ToolExecutionResponse])
async def get_tool_executions(
    tool_name: str,
    limit: int = 50,
    execution_repo: ToolExecutionHistoryRepository = Depends(get_tool_execution_history_repository)
):
    """Get execution history for a specific tool."""
    try:
        executions = execution_repo.get_by_tool_name(tool_name, limit)

        return [
            ToolExecutionResponse(
                id=exec.execution_id,
                tool_name=exec.tool_name,
                input=exec.input_parameters,
                output=exec.output_result or {},
                success=exec.success,
                execution_time=exec.execution_time,
                timestamp=exec.started_at,
                error=exec.error_message
            )
            for exec in executions
        ]

    except Exception as e:
        logger.error(f"Failed to get tool executions: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve tool executions")


@router.get("/{tool_name}/stats")
async def get_tool_stats(
    tool_name: str,
    execution_repo: ToolExecutionHistoryRepository = Depends(get_tool_execution_history_repository)
):
    """Get execution statistics for a specific tool."""
    try:
        stats = execution_repo.get_execution_stats(tool_name)
        return stats

    except Exception as e:
        logger.error(f"Failed to get tool stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve tool statistics")


@router.post("/{tool_name}/register")
async def register_tool(
    tool_name: str,
    tool_repo: ToolRegistryRepository = Depends(get_tool_repository)
):
    """Register a tool from database configuration into the runtime registry."""
    try:
        # Get tool configuration from database
        tool_config = tool_repo.get_by_name(tool_name)
        if not tool_config:
            raise HTTPException(status_code=404, detail=f"Tool '{tool_name}' not found in database")

        if not tool_config.is_active:
            raise HTTPException(status_code=400, detail=f"Tool '{tool_name}' is not active")

        # Load and register tool
        registry = get_registry(tool_repo)
        tool_loader = get_tool_loader(registry)
        success = tool_loader.reload_tool(tool_name, tool_repo)

        if not success:
            raise HTTPException(status_code=500, detail=f"Failed to register tool '{tool_name}'")

        logger.info(f"Successfully registered tool: {tool_name}")

        return {
            "message": f"Tool '{tool_name}' registered successfully",
            "tool_name": tool_name,
            "status": "active"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to register tool {tool_name}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to register tool")


@router.post("/{tool_name}/unregister")
async def unregister_tool(
    tool_name: str,
    tool_repo: ToolRegistryRepository = Depends(get_tool_repository)
):
    """Unregister a tool from the runtime registry."""
    try:
        # Check if tool exists in registry
        registry = get_registry(tool_repo)
        if not registry or not registry.has_tool(tool_name):
            raise HTTPException(status_code=404, detail=f"Tool '{tool_name}' is not registered")

        # Unload tool
        tool_loader = get_tool_loader(registry)
        success = tool_loader.unload_tool(tool_name)

        if not success:
            raise HTTPException(status_code=500, detail=f"Failed to unregister tool '{tool_name}'")

        logger.info(f"Successfully unregistered tool: {tool_name}")

        return {
            "message": f"Tool '{tool_name}' unregistered successfully",
            "tool_name": tool_name,
            "status": "inactive"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to unregister tool {tool_name}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to unregister tool")


@router.post("/reload-all")
async def reload_all_tools(
    tool_repo: ToolRegistryRepository = Depends(get_tool_repository)
):
    """Reload all active tools from database configuration."""
    try:
        tool_loader = get_tool_loader(get_registry())
        loaded_count = tool_loader.load_tools_from_database(tool_repo)

        logger.info(f"Reloaded {loaded_count} tools from database")

        return {
            "message": f"Successfully reloaded {loaded_count} tools",
            "loaded_count": loaded_count,
            "total_registered": get_registry().count()
        }

    except Exception as e:
        logger.error(f"Failed to reload tools: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to reload tools")


@router.get("/debug/registry")
async def debug_registry():
    """Debug endpoint to show what tools are in the registry."""
    try:
        registry = get_registry()
        available_tools = []
        if registry:
            for tool in registry.list():
                available_tools.append({
                    "name": tool.spec.name,
                    "description": tool.spec.description,
                    "category": getattr(tool.spec, 'category', 'general')
                })

        return {
            "registry_initialized": registry is not None,
            "tool_count": len(available_tools),
            "available_tools": available_tools
        }
    except Exception as e:
        return {
            "error": str(e),
            "registry_initialized": False
        }


async def _update_tool_stats(tool_repo: ToolRegistryRepository, tool_name: str, success: bool, execution_time: float):
    """Background task to update tool statistics."""
    try:
        tool_repo.update_tool_execution_stats(tool_name, success, execution_time)
        tool_repo.increment_usage_count(tool_name)
    except Exception as e:
        logger.error(f"Failed to update tool stats for {tool_name}: {str(e)}")


async def _create_execution_record(execution_repo: ToolExecutionHistoryRepository, execution_data: dict):
    """Background task to create tool execution history record."""
    try:
        execution_repo.create_execution_record(execution_data)
    except Exception as e:
        logger.error(f"Failed to create execution record: {str(e)}")