"""
FastAPI Dependency Injection Module

This module provides dependency injection functions for FastAPI routes,
ensuring proper database session management and repository instantiation.
"""

from typing import Generator
from fastapi import Depends, HTTPException
from sqlalchemy.orm import Session

from ..database.connection import get_db_session
from ..database.repositories import (
    OrganizationRepository,
    UserRepository,
    AgentTypeRepository,
    AgentRepository,
    MemoryConfigurationRepository,
    KnowledgeBaseRepository,
    ToolRegistryRepository,
    ToolExecutionHistoryRepository,
    AssociationRepository
)
from ..observability.logging import get_logger

logger = get_logger(__name__)


def get_db() -> Generator[Session, None, None]:
    """
    Database session dependency.

    Provides a database session for FastAPI routes with proper
    session management and cleanup.

    Yields:
        Session: SQLAlchemy database session
    """
    db_generator = get_db_session()
    db = next(db_generator)
    try:
        yield db
    except HTTPException:
        # Don't catch HTTPExceptions as database errors - let them pass through
        raise
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def get_organization_repository(db: Session = Depends(get_db)) -> OrganizationRepository:
    """
    Organization repository dependency.
    
    Args:
        db: Database session
        
    Returns:
        OrganizationRepository: Repository instance for organization operations
    """
    return OrganizationRepository(db)


def get_user_repository(db: Session = Depends(get_db)) -> UserRepository:
    """
    User repository dependency.
    
    Args:
        db: Database session
        
    Returns:
        UserRepository: Repository instance for user operations
    """
    return UserRepository(db)


def get_agent_type_repository(db: Session = Depends(get_db)) -> AgentTypeRepository:
    """
    Agent type repository dependency.
    
    Args:
        db: Database session
        
    Returns:
        AgentTypeRepository: Repository instance for agent type operations
    """
    return AgentTypeRepository(db)


def get_agent_repository(db: Session = Depends(get_db)) -> AgentRepository:
    """
    Agent repository dependency.
    
    Args:
        db: Database session
        
    Returns:
        AgentRepository: Repository instance for agent operations
    """
    return AgentRepository(db)


def get_memory_configuration_repository(db: Session = Depends(get_db)) -> MemoryConfigurationRepository:
    """
    Memory configuration repository dependency.
    
    Args:
        db: Database session
        
    Returns:
        MemoryConfigurationRepository: Repository instance for memory configuration operations
    """
    return MemoryConfigurationRepository(db)


def get_knowledge_base_repository(db: Session = Depends(get_db)) -> KnowledgeBaseRepository:
    """
    Knowledge base repository dependency.
    
    Args:
        db: Database session
        
    Returns:
        KnowledgeBaseRepository: Repository instance for knowledge base operations
    """
    return KnowledgeBaseRepository(db)


def get_tool_repository(db: Session = Depends(get_db)) -> ToolRegistryRepository:
    """
    Tool registry repository dependency.

    Args:
        db: Database session

    Returns:
        ToolRegistryRepository: Repository instance for tool registry operations
    """
    return ToolRegistryRepository(db)


def get_tool_execution_history_repository(db: Session = Depends(get_db)) -> ToolExecutionHistoryRepository:
    """
    Tool execution history repository dependency.

    Args:
        db: Database session

    Returns:
        ToolExecutionHistoryRepository: Repository instance for tool execution history operations
    """
    return ToolExecutionHistoryRepository(db)


def get_association_repository(db: Session = Depends(get_db)) -> AssociationRepository:
    """
    Association repository dependency.
    
    Args:
        db: Database session
        
    Returns:
        AssociationRepository: Repository instance for association operations
    """
    return AssociationRepository(db)


# Health check dependencies
def get_health_status() -> dict:
    """
    Health status dependency.
    
    Returns:
        dict: Health status information
    """
    return {
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00Z",
        "version": "1.0.0"
    }
