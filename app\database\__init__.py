"""
Database module for AgentKit multi-tenant architecture.

This module provides SQLAlchemy models and database utilities for managing
multi-tenant data including organizations, users, agents, agent types,
memory configurations, knowledge bases, and tools registry.
"""

from .models import *
from .connection import get_db_session, init_database, get_database_url
from .repositories import *

__all__ = [
    # Models
    'Organization',
    'User',
    'AgentType',
    'Agent',
    'MemoryConfiguration',
    'KnowledgeBase',
    'ToolRegistry',
    'ToolExecutionHistory',
    'AgentTypeToolAssociation',
    'AgentTypeMemoryAssociation',
    'AgentTypeKnowledgeAssociation',

    # Database utilities
    'get_db_session',
    'init_database',
    'get_database_url',

    # Repositories
    'OrganizationRepository',
    'UserRepository',
    'AgentTypeRepository',
    'AgentRepository',
    'MemoryConfigurationRepository',
    'KnowledgeBaseRepository',
    'ToolRegistryRepository',
    'AssociationRepository',
]
