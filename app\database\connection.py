"""
Database connection and session management for AgentKit.

This module provides SQLAlchemy database connection utilities,
session management, and database initialization functions.
"""

import os
from typing import Generator
from sqlalchemy import create_engine, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from ..core.settings import get_settings
from ..observability.logging import get_logger

logger = get_logger(__name__)

# SQLAlchemy base class for all models
Base = declarative_base()

# Global database engine and session factory
_engine = None
_SessionLocal = None


def get_database_url() -> str:
    """Get the database URL from settings."""
    settings = get_settings()
    return settings.get_database_url("sqlite")


def create_database_engine():
    """Create and configure the database engine."""
    global _engine
    
    if _engine is not None:
        return _engine
    
    database_url = get_database_url()
    logger.info(f"Creating database engine with URL: {database_url}")
    
    # SQLite-specific configuration
    if database_url.startswith("sqlite"):
        # Ensure the directory exists
        db_path = database_url.replace("sqlite:///", "")
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        _engine = create_engine(
            database_url,
            poolclass=StaticPool,
            connect_args={
                "check_same_thread": False,
                "timeout": 20
            },
            echo=get_settings().is_development  # Enable SQL logging in development
        )
        
        # Enable foreign key constraints for SQLite
        @event.listens_for(_engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            cursor = dbapi_connection.cursor()
            cursor.execute("PRAGMA foreign_keys=ON")
            cursor.execute("PRAGMA journal_mode=WAL")  # Better concurrency
            cursor.close()
    else:
        # PostgreSQL or other database configuration
        _engine = create_engine(
            database_url,
            echo=get_settings().is_development
        )
    
    return _engine


def create_session_factory():
    """Create the session factory."""
    global _SessionLocal
    
    if _SessionLocal is not None:
        return _SessionLocal
    
    engine = create_database_engine()
    _SessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=engine
    )
    
    return _SessionLocal


def get_db_session() -> Generator[Session, None, None]:
    """
    Get a database session.
    
    This function provides a database session that can be used
    as a dependency in FastAPI endpoints.
    
    Yields:
        Session: SQLAlchemy database session
    """
    SessionLocal = create_session_factory()
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def init_database():
    """
    Initialize the database by creating all tables.
    
    This function should be called during application startup
    to ensure all database tables are created.
    """
    try:
        # Import all models to ensure they are registered with Base
        from . import models
        
        engine = create_database_engine()
        logger.info("Creating database tables...")
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        
        logger.info("Database tables created successfully")
        
        # Create default organization if it doesn't exist
        _create_default_data()
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


def _create_default_data():
    """Create default organization and admin user if they don't exist."""
    try:
        from .repositories import OrganizationRepository, UserRepository
        
        SessionLocal = create_session_factory()
        db = SessionLocal()
        
        try:
            org_repo = OrganizationRepository(db)
            user_repo = UserRepository(db)
            
            # Create default organization
            default_org = org_repo.get_by_name("Default Organization")
            if not default_org:
                default_org = org_repo.create({
                    "name": "Default Organization",
                    "description": "Default organization for AgentKit",
                    "is_active": True
                })
                logger.info("Created default organization")
            
            # Create default admin user
            admin_user = user_repo.get_by_email("<EMAIL>")
            if not admin_user:
                user_repo.create({
                    "email": "<EMAIL>",
                    "username": "admin",
                    "full_name": "System Administrator",
                    "organization_id": default_org.id,
                    "is_active": True,
                    "is_admin": True
                })
                logger.info("Created default admin user")
            
            db.commit()
            
        except Exception as e:
            db.rollback()
            logger.error(f"Failed to create default data: {e}")
            raise
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error in _create_default_data: {e}")
        # Don't raise here as this is not critical for startup
