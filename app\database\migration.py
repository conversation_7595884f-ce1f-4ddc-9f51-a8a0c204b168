"""
Data migration utilities for AgentKit.

This module provides utilities to migrate existing YAML configurations
to the new SQLite database schema for multi-tenant support.
"""

import os
import yaml
from typing import Dict, Any, List
from pathlib import Path

from .connection import get_db_session
from .repositories import (
    OrganizationRepository, AgentTypeRepository, MemoryConfigurationRepository,
    ToolRegistryRepository, KnowledgeBaseRepository, AssociationRepository
)
from ..observability.logging import get_logger

logger = get_logger(__name__)


class DataMigration:
    """Handles migration of YAML configurations to database."""
    
    def __init__(self):
        self.db_session = next(get_db_session())
        self.org_repo = OrganizationRepository(self.db_session)
        self.agent_type_repo = AgentTypeRepository(self.db_session)
        self.memory_repo = MemoryConfigurationRepository(self.db_session)
        self.tool_repo = ToolRegistryRepository(self.db_session)
        self.kb_repo = KnowledgeBaseRepository(self.db_session)
        self.assoc_repo = AssociationRepository(self.db_session)
    
    def migrate_all(self):
        """Run complete migration from YAML to database."""
        try:
            logger.info("Starting data migration from YAML to database")
            
            # Ensure default organization exists
            default_org = self._ensure_default_organization()
            
            # Migrate agent types from YAML files
            self._migrate_agent_types(default_org.id)
            
            # Migrate default tools
            self._migrate_default_tools()
            
            # Migrate default memory configurations
            self._migrate_default_memory_configs()
            
            # Migrate default knowledge bases
            self._migrate_default_knowledge_bases()
            
            logger.info("Data migration completed successfully")
            
        except Exception as e:
            logger.error(f"Data migration failed: {e}")
            self.db_session.rollback()
            raise
        finally:
            self.db_session.close()
    
    def _ensure_default_organization(self):
        """Ensure default organization exists."""
        org = self.org_repo.get_by_name("Default Organization")
        if not org:
            org = self.org_repo.create({
                "name": "Default Organization",
                "description": "Default organization for migrated configurations",
                "is_active": True
            })
            logger.info(f"Created default organization: {org.id}")
        return org
    
    def _migrate_agent_types(self, organization_id: int):
        """Migrate agent types from YAML files."""
        config_dir = Path("app/config/agents")
        if not config_dir.exists():
            logger.warning(f"Agent config directory not found: {config_dir}")
            return
        
        yaml_files = list(config_dir.glob("*.yaml"))
        logger.info(f"Found {len(yaml_files)} agent configuration files")
        
        for yaml_file in yaml_files:
            try:
                self._migrate_single_agent_type(yaml_file, organization_id)
            except Exception as e:
                logger.error(f"Failed to migrate {yaml_file}: {e}")
                continue
    
    def _migrate_single_agent_type(self, yaml_file: Path, organization_id: int):
        """Migrate a single agent type from YAML file."""
        with open(yaml_file, 'r') as f:
            config = yaml.safe_load(f)
        
        agent_name = yaml_file.stem.replace('_', ' ').title()
        
        # Check if agent type already exists
        existing_type = self.agent_type_repo.get_by_name(agent_name, organization_id)
        if existing_type:
            logger.info(f"Agent type '{agent_name}' already exists, skipping")
            return
        
        # Extract agent type data
        agent_type_data = {
            "name": agent_name,
            "description": config.get("description", f"{agent_name} agent type"),
            "category": self._determine_category(agent_name),
            "system_prompt": config.get("system_prompt", ""),
            "instructions": config.get("instructions", ""),
            "configuration": {
                "llm": config.get("llm", {}),
                "tools": config.get("tools", []),
                "memory": config.get("memory", {}),
                "validation": config.get("validation", {}),
                "policy": config.get("policy", {})
            },
            "organization_id": organization_id,
            "is_active": True,
            "is_system": True  # Mark as system-defined
        }
        
        # Create agent type
        agent_type = self.agent_type_repo.create(agent_type_data)
        logger.info(f"Created agent type: {agent_type.name} (ID: {agent_type.id})")
        
        # Associate with tools, memory configs, and knowledge bases
        self._associate_resources(agent_type.id, config)
    
    def _determine_category(self, agent_name: str) -> str:
        """Determine category based on agent name."""
        name_lower = agent_name.lower()
        if "support" in name_lower or "customer" in name_lower:
            return "Support"
        elif "sales" in name_lower:
            return "Sales"
        elif "research" in name_lower:
            return "Research"
        else:
            return "General"
    
    def _migrate_default_tools(self):
        """Migrate default tools to database."""
        default_tools = [
            {
                "name": "http_get",
                "description": "HTTP GET request tool",
                "tool_type": "web",
                "category": "network",
                "module_path": "app.tools.http_get",
                "class_name": "HttpGetTool",
                "configuration": {},
                "parameters_schema": {
                    "type": "object",
                    "properties": {
                        "url": {"type": "string", "description": "URL to fetch"}
                    },
                    "required": ["url"]
                }
            },
            {
                "name": "duckduckgo_search",
                "description": "DuckDuckGo search tool",
                "tool_type": "search",
                "category": "information",
                "module_path": "app.tools.duckduckgo_search",
                "class_name": "DuckDuckGoSearchTool",
                "configuration": {},
                "parameters_schema": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Search query"}
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "searxng_search",
                "description": "SearxNG search tool",
                "tool_type": "search",
                "category": "information",
                "module_path": "app.tools.searxng_search",
                "class_name": "SearxNGSearchTool",
                "configuration": {"url": "http://localhost:8089"},
                "parameters_schema": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Search query"}
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "math_eval",
                "description": "Mathematical expression evaluator",
                "tool_type": "computation",
                "category": "math",
                "module_path": "app.tools.math_eval",
                "class_name": "MathEvalTool",
                "configuration": {},
                "parameters_schema": {
                    "type": "object",
                    "properties": {
                        "expression": {"type": "string", "description": "Mathematical expression"}
                    },
                    "required": ["expression"]
                }
            },
            {
                "name": "markdown_to_pdf",
                "description": "Convert Markdown to PDF",
                "tool_type": "conversion",
                "category": "document",
                "module_path": "app.tools.markdown_to_pdf",
                "class_name": "MarkdownToPdfTool",
                "configuration": {},
                "parameters_schema": {
                    "type": "object",
                    "properties": {
                        "markdown_content": {"type": "string", "description": "Markdown content"},
                        "output_path": {"type": "string", "description": "Output PDF path"}
                    },
                    "required": ["markdown_content"]
                }
            }
        ]
        
        for tool_data in default_tools:
            existing_tool = self.tool_repo.get_by_name(tool_data["name"])
            if not existing_tool:
                tool = self.tool_repo.create(tool_data)
                logger.info(f"Created tool: {tool.name} (ID: {tool.id})")
    
    def _migrate_default_memory_configs(self):
        """Migrate default memory configurations."""
        memory_configs = [
            {
                "name": "In-Memory Short Term",
                "description": "Fast in-memory storage for short-term memory",
                "memory_type": "short_term",
                "adapter_type": "in_memory",
                "adapter_config": {"max_size": 1000, "session_based": True}
            },
            {
                "name": "SQLite Episodic",
                "description": "SQLite storage for episodic memory",
                "memory_type": "episodic",
                "adapter_type": "sqlite",
                "adapter_config": {"sqlite_path": "./data/episodic.sqlite", "max_size": 5000}
            },
            {
                "name": "JSONL Procedural",
                "description": "JSONL file storage for procedural memory",
                "memory_type": "procedural",
                "adapter_type": "jsonl",
                "adapter_config": {"jsonl_path": "./data/procedural", "max_file_size_mb": 50}
            },
            {
                "name": "Vector Semantic",
                "description": "Vector database storage for semantic memory",
                "memory_type": "semantic",
                "adapter_type": "vectordb",
                "adapter_config": {
                    "qdrant_host": "localhost",
                    "qdrant_port": 6333,
                    "collection": "semantic_memory"
                }
            }
        ]
        
        for config_data in memory_configs:
            existing_config = self.memory_repo.get_by_name(config_data["name"])
            if not existing_config:
                config = self.memory_repo.create(config_data)
                logger.info(f"Created memory config: {config.name} (ID: {config.id})")
    
    def _migrate_default_knowledge_bases(self):
        """Migrate default knowledge base configurations."""
        kb_configs = [
            {
                "name": "General Knowledge",
                "description": "General purpose knowledge base",
                "kb_type": "documents",
                "configuration": {"source_type": "files", "formats": ["txt", "md", "pdf"]},
                "storage_config": {"index_type": "vector", "embedding_model": "sentence-transformers"}
            }
        ]
        
        for kb_data in kb_configs:
            existing_kb = self.kb_repo.get_by_name(kb_data["name"])
            if not existing_kb:
                kb = self.kb_repo.create(kb_data)
                logger.info(f"Created knowledge base: {kb.name} (ID: {kb.id})")
    
    def _associate_resources(self, agent_type_id: int, config: Dict[str, Any]):
        """Associate tools, memory configs, and knowledge bases with agent type."""
        # This would be implemented based on the specific configuration structure
        # For now, we'll associate default resources
        pass


def run_migration():
    """Run the data migration."""
    migration = DataMigration()
    migration.migrate_all()


if __name__ == "__main__":
    run_migration()
