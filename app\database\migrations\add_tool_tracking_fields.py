"""
Database Migration: Add Tool Tracking Fields

This migration adds usage tracking, performance metrics, and metadata fields
to the ToolRegistry table for comprehensive tool management.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from sqlalchemy import text
from sqlalchemy.orm import Session
from loguru import logger

from app.database.connection import get_db_session


def upgrade():
    """Add new fields to ToolRegistry table."""
    db = get_db_session()
    
    try:
        # Add new columns to tool_registry table
        migrations = [
            # Tool metadata fields
            "ALTER TABLE tool_registry ADD COLUMN output_schema JSON",
            "ALTER TABLE tool_registry ADD COLUMN version VARCHAR(20)",
            "ALTER TABLE tool_registry ADD COLUMN author VARCHAR(100)",
            "ALTER TABLE tool_registry ADD COLUMN tags JSON",
            
            # Usage tracking fields
            "ALTER TABLE tool_registry ADD COLUMN usage_count INTEGER DEFAULT 0 NOT NULL",
            "ALTER TABLE tool_registry ADD COLUMN last_used TIMESTAMP WITH TIME ZONE",
            "ALTER TABLE tool_registry ADD COLUMN execution_count INTEGER DEFAULT 0 NOT NULL",
            "ALTER TABLE tool_registry ADD COLUMN success_count INTEGER DEFAULT 0 NOT NULL",
            "ALTER TABLE tool_registry ADD COLUMN error_count INTEGER DEFAULT 0 NOT NULL",
            
            # Performance metrics fields
            "ALTER TABLE tool_registry ADD COLUMN avg_execution_time REAL",
            "ALTER TABLE tool_registry ADD COLUMN total_execution_time REAL DEFAULT 0.0 NOT NULL",
        ]
        
        for migration in migrations:
            try:
                db.execute(text(migration))
                logger.info(f"Executed migration: {migration}")
            except Exception as e:
                # Column might already exist, log warning and continue
                logger.warning(f"Migration failed (column might exist): {migration} - {e}")
        
        db.commit()
        logger.info("Tool tracking fields migration completed successfully")
        
    except Exception as e:
        db.rollback()
        logger.error(f"Migration failed: {e}")
        raise
    finally:
        db.close()


def downgrade():
    """Remove the added fields from ToolRegistry table."""
    db = get_db_session()
    
    try:
        # Remove columns (SQLite doesn't support DROP COLUMN directly)
        # This would require recreating the table in SQLite
        logger.warning("Downgrade not implemented for SQLite. Manual intervention required.")
        
        # For PostgreSQL/MySQL, you would use:
        # migrations = [
        #     "ALTER TABLE tool_registry DROP COLUMN output_schema",
        #     "ALTER TABLE tool_registry DROP COLUMN version",
        #     "ALTER TABLE tool_registry DROP COLUMN author",
        #     "ALTER TABLE tool_registry DROP COLUMN tags",
        #     "ALTER TABLE tool_registry DROP COLUMN usage_count",
        #     "ALTER TABLE tool_registry DROP COLUMN last_used",
        #     "ALTER TABLE tool_registry DROP COLUMN execution_count",
        #     "ALTER TABLE tool_registry DROP COLUMN success_count",
        #     "ALTER TABLE tool_registry DROP COLUMN error_count",
        #     "ALTER TABLE tool_registry DROP COLUMN avg_execution_time",
        #     "ALTER TABLE tool_registry DROP COLUMN total_execution_time",
        # ]
        
    except Exception as e:
        db.rollback()
        logger.error(f"Downgrade failed: {e}")
        raise
    finally:
        db.close()


if __name__ == "__main__":
    """Run migration directly."""
    logger.info("Running tool tracking fields migration...")
    upgrade()
    logger.info("Migration completed!")
