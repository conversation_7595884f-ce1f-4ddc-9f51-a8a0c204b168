"""
SQLAlchemy models for AgentKit multi-tenant architecture.

This module defines all database models for organizations, users, agents,
agent types, memory configurations, knowledge bases, and tools registry.
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy import (
    Column, Integer, String, Text, Boolean, DateTime, Float,
    ForeignKey, JSON, UniqueConstraint, Index
)
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func

from .connection import Base


class Organization(Base):
    """Organization model for multi-tenant support."""
    
    __tablename__ = "organizations"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(255), unique=True, nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    users: Mapped[List["User"]] = relationship("User", back_populates="organization", cascade="all, delete-orphan")
    agents: Mapped[List["Agent"]] = relationship("Agent", back_populates="organization", cascade="all, delete-orphan")
    agent_types: Mapped[List["AgentType"]] = relationship("AgentType", back_populates="organization", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Organization(id={self.id}, name='{self.name}')>"


class User(Base):
    """User model with organization association."""
    
    __tablename__ = "users"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    email: Mapped[str] = mapped_column(String(255), unique=True, nullable=False, index=True)
    username: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    full_name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_admin: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # Organization association
    organization_id: Mapped[int] = mapped_column(Integer, ForeignKey("organizations.id"), nullable=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_login: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    organization: Mapped["Organization"] = relationship("Organization", back_populates="users")
    agents: Mapped[List["Agent"]] = relationship("Agent", back_populates="created_by")
    
    # Unique constraint for username within organization
    __table_args__ = (
        UniqueConstraint('username', 'organization_id', name='uq_user_username_org'),
        Index('idx_user_org_active', 'organization_id', 'is_active'),
    )
    
    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}', org_id={self.organization_id})>"


class AgentType(Base):
    """Agent type model for defining different categories of agents."""
    
    __tablename__ = "agent_types"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    category: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, index=True)
    
    # Configuration stored as JSON
    configuration: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    
    # System prompt and instructions
    system_prompt: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    instructions: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Organization association
    organization_id: Mapped[int] = mapped_column(Integer, ForeignKey("organizations.id"), nullable=False)
    
    # Status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_system: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)  # System-defined types
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    organization: Mapped["Organization"] = relationship("Organization", back_populates="agent_types")
    agents: Mapped[List["Agent"]] = relationship("Agent", back_populates="agent_type", cascade="all, delete-orphan")
    
    # Many-to-many relationships
    tools: Mapped[List["ToolRegistry"]] = relationship(
        "ToolRegistry", 
        secondary="agent_type_tool_associations",
        back_populates="agent_types"
    )
    memory_configs: Mapped[List["MemoryConfiguration"]] = relationship(
        "MemoryConfiguration",
        secondary="agent_type_memory_associations", 
        back_populates="agent_types"
    )
    knowledge_bases: Mapped[List["KnowledgeBase"]] = relationship(
        "KnowledgeBase",
        secondary="agent_type_knowledge_associations",
        back_populates="agent_types"
    )
    
    # Unique constraint for name within organization
    __table_args__ = (
        UniqueConstraint('name', 'organization_id', name='uq_agent_type_name_org'),
        Index('idx_agent_type_org_active', 'organization_id', 'is_active'),
    )
    
    def __repr__(self):
        return f"<AgentType(id={self.id}, name='{self.name}', org_id={self.organization_id})>"


class Agent(Base):
    """Agent model for individual agent instances."""
    
    __tablename__ = "agents"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Agent configuration
    configuration: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    
    # Status and metadata
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    version: Mapped[str] = mapped_column(String(20), default="1.0.0", nullable=False)
    
    # Associations
    agent_type_id: Mapped[int] = mapped_column(Integer, ForeignKey("agent_types.id"), nullable=False)
    organization_id: Mapped[int] = mapped_column(Integer, ForeignKey("organizations.id"), nullable=False)
    created_by_id: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_used: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    agent_type: Mapped["AgentType"] = relationship("AgentType", back_populates="agents")
    organization: Mapped["Organization"] = relationship("Organization", back_populates="agents")
    created_by: Mapped["User"] = relationship("User", back_populates="agents")
    
    # Unique constraint for name within organization and agent type
    __table_args__ = (
        UniqueConstraint('name', 'organization_id', 'agent_type_id', name='uq_agent_name_org_type'),
        Index('idx_agent_org_type_active', 'organization_id', 'agent_type_id', 'is_active'),
    )
    
    def __repr__(self):
        return f"<Agent(id={self.id}, name='{self.name}', type_id={self.agent_type_id})>"


class MemoryConfiguration(Base):
    """Memory configuration model for different memory types and adapters."""

    __tablename__ = "memory_configurations"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Memory type (short_term, episodic, procedural, semantic)
    memory_type: Mapped[str] = mapped_column(String(50), nullable=False, index=True)

    # Adapter configuration
    adapter_type: Mapped[str] = mapped_column(String(50), nullable=False)  # in_memory, sqlite, redis, etc.
    adapter_config: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)

    # Global configuration (not organization-specific)
    is_global: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    agent_types: Mapped[List["AgentType"]] = relationship(
        "AgentType",
        secondary="agent_type_memory_associations",
        back_populates="memory_configs"
    )

    def __repr__(self):
        return f"<MemoryConfiguration(id={self.id}, name='{self.name}', type='{self.memory_type}')>"


class KnowledgeBase(Base):
    """Knowledge base model for storing and managing knowledge sources."""

    __tablename__ = "knowledge_bases"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Knowledge base type (documents, web, api, database, etc.)
    kb_type: Mapped[str] = mapped_column(String(50), nullable=False, index=True)

    # Configuration for the knowledge base
    configuration: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)

    # Storage and indexing configuration
    storage_config: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)

    # Global configuration (not organization-specific)
    is_global: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    agent_types: Mapped[List["AgentType"]] = relationship(
        "AgentType",
        secondary="agent_type_knowledge_associations",
        back_populates="knowledge_bases"
    )

    def __repr__(self):
        return f"<KnowledgeBase(id={self.id}, name='{self.name}', type='{self.kb_type}')>"


class ToolRegistry(Base):
    """Tool registry model for managing available tools."""

    __tablename__ = "tool_registry"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(100), unique=True, nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Tool type and category
    tool_type: Mapped[str] = mapped_column(String(50), nullable=False, index=True)
    category: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, index=True)

    # Tool implementation details
    module_path: Mapped[str] = mapped_column(String(255), nullable=False)
    class_name: Mapped[str] = mapped_column(String(100), nullable=False)

    # Tool configuration and parameters
    configuration: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    parameters_schema: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    output_schema: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)

    # Tool metadata
    version: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    author: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    tags: Mapped[Optional[List[str]]] = mapped_column(JSON, nullable=True)

    # Usage tracking
    usage_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    last_used: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    execution_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    success_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    error_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)

    # Performance metrics
    avg_execution_time: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    total_execution_time: Mapped[Optional[float]] = mapped_column(Float, default=0.0, nullable=False)

    # Global tool (available to all organizations)
    is_global: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    agent_types: Mapped[List["AgentType"]] = relationship(
        "AgentType",
        secondary="agent_type_tool_associations",
        back_populates="tools"
    )

    def __repr__(self):
        return f"<ToolRegistry(id={self.id}, name='{self.name}', type='{self.tool_type}')>"

    @property
    def success_rate(self) -> float:
        """Calculate tool success rate."""
        if self.execution_count == 0:
            return 0.0
        return (self.success_count / self.execution_count) * 100

    def update_execution_stats(self, success: bool, execution_time: float) -> None:
        """Update tool execution statistics."""
        self.execution_count += 1
        self.last_used = func.now()
        self.total_execution_time += execution_time

        if success:
            self.success_count += 1
        else:
            self.error_count += 1

        # Update average execution time
        self.avg_execution_time = self.total_execution_time / self.execution_count


class ToolExecutionHistory(Base):
    """Tool execution history model for tracking individual tool executions."""

    __tablename__ = "tool_execution_history"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    execution_id: Mapped[str] = mapped_column(String(100), unique=True, nullable=False, index=True)

    # Tool reference
    tool_id: Mapped[int] = mapped_column(Integer, ForeignKey("tool_registry.id"), nullable=False)
    tool_name: Mapped[str] = mapped_column(String(100), nullable=False, index=True)

    # Execution details
    input_parameters: Mapped[dict] = mapped_column(JSON, nullable=False)
    output_result: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    success: Mapped[bool] = mapped_column(Boolean, nullable=False)
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Performance metrics
    execution_time: Mapped[float] = mapped_column(Float, nullable=False)
    memory_usage: Mapped[Optional[float]] = mapped_column(Float, nullable=True)

    # Context information
    user_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    session_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    agent_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)

    # Timestamps
    started_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    completed_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    tool: Mapped["ToolRegistry"] = relationship("ToolRegistry", backref="execution_history")

    def __repr__(self):
        return f"<ToolExecutionHistory(id={self.id}, tool='{self.tool_name}', success={self.success})>"


# Association tables for many-to-many relationships

class AgentTypeToolAssociation(Base):
    """Association table for agent types and tools."""

    __tablename__ = "agent_type_tool_associations"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    agent_type_id: Mapped[int] = mapped_column(Integer, ForeignKey("agent_types.id"), nullable=False)
    tool_id: Mapped[int] = mapped_column(Integer, ForeignKey("tool_registry.id"), nullable=False)

    # Configuration specific to this association
    configuration: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    is_enabled: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Unique constraint
    __table_args__ = (
        UniqueConstraint('agent_type_id', 'tool_id', name='uq_agent_type_tool'),
    )


class AgentTypeMemoryAssociation(Base):
    """Association table for agent types and memory configurations."""

    __tablename__ = "agent_type_memory_associations"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    agent_type_id: Mapped[int] = mapped_column(Integer, ForeignKey("agent_types.id"), nullable=False)
    memory_config_id: Mapped[int] = mapped_column(Integer, ForeignKey("memory_configurations.id"), nullable=False)

    # Configuration specific to this association
    configuration: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    is_enabled: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Unique constraint
    __table_args__ = (
        UniqueConstraint('agent_type_id', 'memory_config_id', name='uq_agent_type_memory'),
    )


class AgentTypeKnowledgeAssociation(Base):
    """Association table for agent types and knowledge bases."""

    __tablename__ = "agent_type_knowledge_associations"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    agent_type_id: Mapped[int] = mapped_column(Integer, ForeignKey("agent_types.id"), nullable=False)
    knowledge_base_id: Mapped[int] = mapped_column(Integer, ForeignKey("knowledge_bases.id"), nullable=False)

    # Configuration specific to this association
    configuration: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    is_enabled: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Unique constraint
    __table_args__ = (
        UniqueConstraint('agent_type_id', 'knowledge_base_id', name='uq_agent_type_knowledge'),
    )
