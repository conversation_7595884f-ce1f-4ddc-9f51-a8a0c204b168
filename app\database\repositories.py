"""
Repository classes for database operations.

This module provides repository pattern implementations for all database models,
offering a clean interface for CRUD operations and business logic.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from .models import (
    Organization, User, AgentType, Agent, MemoryConfiguration,
    KnowledgeBase, ToolRegistry, ToolExecutionHistory, AgentTypeToolAssociation,
    AgentTypeMemoryAssociation, AgentTypeKnowledgeAssociation
)
from ..observability.logging import get_logger

logger = get_logger(__name__)


class BaseRepository:
    """Base repository class with common CRUD operations."""
    
    def __init__(self, db: Session, model_class):
        self.db = db
        self.model_class = model_class
    
    def get_by_id(self, id: int) -> Optional[Any]:
        """Get a record by ID."""
        return self.db.query(self.model_class).filter(self.model_class.id == id).first()
    
    def get_all(self, skip: int = 0, limit: int = 100) -> List[Any]:
        """Get all records with pagination."""
        return self.db.query(self.model_class).offset(skip).limit(limit).all()
    
    def create(self, data: Dict[str, Any]) -> Any:
        """Create a new record."""
        try:
            obj = self.model_class(**data)
            self.db.add(obj)
            self.db.commit()
            self.db.refresh(obj)
            logger.info(f"Created {self.model_class.__name__} with ID: {obj.id}")
            return obj
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create {self.model_class.__name__}: {e}")
            raise
    
    def update(self, id: int, data: Dict[str, Any]) -> Optional[Any]:
        """Update a record by ID."""
        try:
            obj = self.get_by_id(id)
            if not obj:
                return None
            
            for key, value in data.items():
                if hasattr(obj, key):
                    setattr(obj, key, value)
            
            self.db.commit()
            self.db.refresh(obj)
            logger.info(f"Updated {self.model_class.__name__} with ID: {id}")
            return obj
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to update {self.model_class.__name__} {id}: {e}")
            raise
    
    def delete(self, id: int) -> bool:
        """Delete a record by ID."""
        try:
            obj = self.get_by_id(id)
            if not obj:
                return False
            
            self.db.delete(obj)
            self.db.commit()
            logger.info(f"Deleted {self.model_class.__name__} with ID: {id}")
            return True
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to delete {self.model_class.__name__} {id}: {e}")
            raise


class OrganizationRepository(BaseRepository):
    """Repository for Organization model."""
    
    def __init__(self, db: Session):
        super().__init__(db, Organization)
    
    def get_by_name(self, name: str) -> Optional[Organization]:
        """Get organization by name."""
        return self.db.query(Organization).filter(Organization.name == name).first()
    
    def get_active_organizations(self) -> List[Organization]:
        """Get all active organizations."""
        return self.db.query(Organization).filter(Organization.is_active == True).all()


class UserRepository(BaseRepository):
    """Repository for User model."""
    
    def __init__(self, db: Session):
        super().__init__(db, User)
    
    def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        return self.db.query(User).filter(User.email == email).first()
    
    def get_by_username(self, username: str, organization_id: int) -> Optional[User]:
        """Get user by username within organization."""
        return self.db.query(User).filter(
            and_(User.username == username, User.organization_id == organization_id)
        ).first()
    
    def get_by_organization(self, organization_id: int, skip: int = 0, limit: int = 100) -> List[User]:
        """Get users by organization."""
        return self.db.query(User).filter(
            User.organization_id == organization_id
        ).offset(skip).limit(limit).all()
    
    def get_active_users(self, organization_id: int) -> List[User]:
        """Get active users in organization."""
        return self.db.query(User).filter(
            and_(User.organization_id == organization_id, User.is_active == True)
        ).all()


class AgentTypeRepository(BaseRepository):
    """Repository for AgentType model."""
    
    def __init__(self, db: Session):
        super().__init__(db, AgentType)
    
    def get_by_name(self, name: str, organization_id: int) -> Optional[AgentType]:
        """Get agent type by name within organization."""
        return self.db.query(AgentType).filter(
            and_(AgentType.name == name, AgentType.organization_id == organization_id)
        ).first()
    
    def get_by_organization(self, organization_id: int, skip: int = 0, limit: int = 100) -> List[AgentType]:
        """Get agent types by organization."""
        return self.db.query(AgentType).filter(
            AgentType.organization_id == organization_id
        ).offset(skip).limit(limit).all()
    
    def get_active_types(self, organization_id: int) -> List[AgentType]:
        """Get active agent types in organization."""
        return self.db.query(AgentType).filter(
            and_(AgentType.organization_id == organization_id, AgentType.is_active == True)
        ).all()
    
    def get_by_category(self, category: str, organization_id: int) -> List[AgentType]:
        """Get agent types by category within organization."""
        return self.db.query(AgentType).filter(
            and_(AgentType.category == category, AgentType.organization_id == organization_id)
        ).all()

    def count_by_organization(self, organization_id: int, category: str = None) -> int:
        """Count agent types in organization, optionally filtered by category."""
        query = self.db.query(AgentType).filter(AgentType.organization_id == organization_id)
        if category:
            query = query.filter(AgentType.category == category)
        return query.count()


class AgentRepository(BaseRepository):
    """Repository for Agent model."""
    
    def __init__(self, db: Session):
        super().__init__(db, Agent)
    
    def get_by_name(self, name: str, organization_id: int, agent_type_id: int) -> Optional[Agent]:
        """Get agent by name within organization and type."""
        return self.db.query(Agent).filter(
            and_(
                Agent.name == name,
                Agent.organization_id == organization_id,
                Agent.agent_type_id == agent_type_id
            )
        ).first()
    
    def get_by_organization(self, organization_id: int, skip: int = 0, limit: int = 100) -> List[Agent]:
        """Get agents by organization."""
        return self.db.query(Agent).filter(
            Agent.organization_id == organization_id
        ).offset(skip).limit(limit).all()
    
    def get_by_type(self, agent_type_id: int, organization_id: int) -> List[Agent]:
        """Get agents by type within organization."""
        return self.db.query(Agent).filter(
            and_(Agent.agent_type_id == agent_type_id, Agent.organization_id == organization_id)
        ).all()
    
    def get_active_agents(self, organization_id: int) -> List[Agent]:
        """Get active agents in organization."""
        return self.db.query(Agent).filter(
            and_(Agent.organization_id == organization_id, Agent.is_active == True)
        ).all()
    
    def get_by_user(self, user_id: int, organization_id: int) -> List[Agent]:
        """Get agents created by user."""
        return self.db.query(Agent).filter(
            and_(Agent.created_by_id == user_id, Agent.organization_id == organization_id)
        ).all()


class MemoryConfigurationRepository(BaseRepository):
    """Repository for MemoryConfiguration model."""

    def __init__(self, db: Session):
        super().__init__(db, MemoryConfiguration)

    def get_by_name(self, name: str) -> Optional[MemoryConfiguration]:
        """Get memory configuration by name."""
        return self.db.query(MemoryConfiguration).filter(MemoryConfiguration.name == name).first()

    def get_by_type(self, memory_type: str) -> List[MemoryConfiguration]:
        """Get memory configurations by type."""
        return self.db.query(MemoryConfiguration).filter(
            MemoryConfiguration.memory_type == memory_type
        ).all()

    def get_global_configs(self) -> List[MemoryConfiguration]:
        """Get global memory configurations."""
        return self.db.query(MemoryConfiguration).filter(
            and_(MemoryConfiguration.is_global == True, MemoryConfiguration.is_active == True)
        ).all()


class KnowledgeBaseRepository(BaseRepository):
    """Repository for KnowledgeBase model."""

    def __init__(self, db: Session):
        super().__init__(db, KnowledgeBase)

    def get_by_name(self, name: str) -> Optional[KnowledgeBase]:
        """Get knowledge base by name."""
        return self.db.query(KnowledgeBase).filter(KnowledgeBase.name == name).first()

    def get_by_type(self, kb_type: str) -> List[KnowledgeBase]:
        """Get knowledge bases by type."""
        return self.db.query(KnowledgeBase).filter(KnowledgeBase.kb_type == kb_type).all()

    def get_global_knowledge_bases(self) -> List[KnowledgeBase]:
        """Get global knowledge bases."""
        return self.db.query(KnowledgeBase).filter(
            and_(KnowledgeBase.is_global == True, KnowledgeBase.is_active == True)
        ).all()


class ToolRegistryRepository(BaseRepository):
    """Repository for ToolRegistry model."""

    def __init__(self, db: Session):
        super().__init__(db, ToolRegistry)

    def get_by_name(self, name: str) -> Optional[ToolRegistry]:
        """Get tool by name."""
        return self.db.query(ToolRegistry).filter(ToolRegistry.name == name).first()

    def get_by_type(self, tool_type: str) -> List[ToolRegistry]:
        """Get tools by type."""
        return self.db.query(ToolRegistry).filter(ToolRegistry.tool_type == tool_type).all()

    def get_by_category(self, category: str) -> List[ToolRegistry]:
        """Get tools by category."""
        return self.db.query(ToolRegistry).filter(ToolRegistry.category == category).all()

    def get_global_tools(self) -> List[ToolRegistry]:
        """Get global tools."""
        return self.db.query(ToolRegistry).filter(
            and_(ToolRegistry.is_global == True, ToolRegistry.is_active == True)
        ).all()

    def get_categories(self) -> List[str]:
        """Get all unique tool categories."""
        try:
            result = self.db.query(ToolRegistry.category).filter(
                ToolRegistry.category.isnot(None)
            ).distinct().all()
            return [category[0] for category in result if category[0]]
        except Exception as e:
            logger.error(f"Failed to get tool categories: {e}")
            return []

    def increment_usage_count(self, tool_name: str) -> bool:
        """Increment usage count for a tool."""
        try:
            tool = self.get_by_name(tool_name)
            if not tool:
                return False

            # Add usage_count field if it doesn't exist (for backward compatibility)
            if not hasattr(tool, 'usage_count'):
                # This would require a database migration to add the field
                logger.warning(f"Tool {tool_name} doesn't have usage_count field")
                return False

            tool.usage_count = (tool.usage_count or 0) + 1
            tool.updated_at = func.now()
            self.db.commit()
            logger.debug(f"Incremented usage count for tool: {tool_name}")
            return True
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to increment usage count for {tool_name}: {e}")
            return False

    def get_active_tools(self) -> List[ToolRegistry]:
        """Get all active tools."""
        return self.db.query(ToolRegistry).filter(ToolRegistry.is_active == True).all()

    def search_tools(self, query: str) -> List[ToolRegistry]:
        """Search tools by name or description."""
        search_pattern = f"%{query}%"
        return self.db.query(ToolRegistry).filter(
            or_(
                ToolRegistry.name.ilike(search_pattern),
                ToolRegistry.description.ilike(search_pattern)
            )
        ).all()

    def get_tools_by_status(self, is_active: bool) -> List[ToolRegistry]:
        """Get tools by active status."""
        return self.db.query(ToolRegistry).filter(ToolRegistry.is_active == is_active).all()

    def update_tool_execution_stats(self, tool_name: str, success: bool, execution_time: float) -> bool:
        """Update tool execution statistics."""
        try:
            tool = self.get_by_name(tool_name)
            if not tool:
                return False

            tool.update_execution_stats(success, execution_time)
            self.db.commit()
            logger.debug(f"Updated execution stats for tool: {tool_name}")
            return True
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to update execution stats for {tool_name}: {e}")
            return False


class ToolExecutionHistoryRepository(BaseRepository):
    """Repository for ToolExecutionHistory model."""

    def __init__(self, db: Session):
        super().__init__(db, ToolExecutionHistory)

    def create_execution_record(self, execution_data: Dict[str, Any]) -> ToolExecutionHistory:
        """Create a new tool execution record."""
        try:
            execution = ToolExecutionHistory(**execution_data)
            self.db.add(execution)
            self.db.commit()
            self.db.refresh(execution)
            logger.info(f"Created execution record: {execution.execution_id}")
            return execution
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create execution record: {e}")
            raise

    def get_by_execution_id(self, execution_id: str) -> Optional[ToolExecutionHistory]:
        """Get execution record by execution ID."""
        return self.db.query(ToolExecutionHistory).filter(
            ToolExecutionHistory.execution_id == execution_id
        ).first()

    def get_by_tool_name(self, tool_name: str, limit: int = 100) -> List[ToolExecutionHistory]:
        """Get execution history for a specific tool."""
        return self.db.query(ToolExecutionHistory).filter(
            ToolExecutionHistory.tool_name == tool_name
        ).order_by(ToolExecutionHistory.created_at.desc()).limit(limit).all()

    def get_recent_executions(self, limit: int = 50) -> List[ToolExecutionHistory]:
        """Get recent tool executions."""
        return self.db.query(ToolExecutionHistory).order_by(
            ToolExecutionHistory.created_at.desc()
        ).limit(limit).all()

    def get_execution_stats(self, tool_name: Optional[str] = None) -> Dict[str, Any]:
        """Get execution statistics."""
        try:
            query = self.db.query(ToolExecutionHistory)
            if tool_name:
                query = query.filter(ToolExecutionHistory.tool_name == tool_name)

            executions = query.all()

            if not executions:
                return {
                    "total_executions": 0,
                    "successful_executions": 0,
                    "failed_executions": 0,
                    "success_rate": 0.0,
                    "avg_execution_time": 0.0
                }

            total = len(executions)
            successful = sum(1 for e in executions if e.success)
            failed = total - successful
            avg_time = sum(e.execution_time for e in executions) / total

            return {
                "total_executions": total,
                "successful_executions": successful,
                "failed_executions": failed,
                "success_rate": (successful / total) * 100 if total > 0 else 0.0,
                "avg_execution_time": avg_time
            }
        except Exception as e:
            logger.error(f"Failed to get execution stats: {e}")
            return {}


class AssociationRepository:
    """Repository for managing associations between agent types and resources."""

    def __init__(self, db: Session):
        self.db = db

    def add_tool_to_agent_type(self, agent_type_id: int, tool_id: int, configuration: Optional[Dict[str, Any]] = None) -> AgentTypeToolAssociation:
        """Add a tool to an agent type."""
        try:
            association = AgentTypeToolAssociation(
                agent_type_id=agent_type_id,
                tool_id=tool_id,
                configuration=configuration
            )
            self.db.add(association)
            self.db.commit()
            self.db.refresh(association)
            return association
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to add tool {tool_id} to agent type {agent_type_id}: {e}")
            raise

    def remove_tool_from_agent_type(self, agent_type_id: int, tool_id: int) -> bool:
        """Remove a tool from an agent type."""
        try:
            association = self.db.query(AgentTypeToolAssociation).filter(
                and_(
                    AgentTypeToolAssociation.agent_type_id == agent_type_id,
                    AgentTypeToolAssociation.tool_id == tool_id
                )
            ).first()

            if association:
                self.db.delete(association)
                self.db.commit()
                return True
            return False
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to remove tool {tool_id} from agent type {agent_type_id}: {e}")
            raise

    def add_memory_to_agent_type(self, agent_type_id: int, memory_config_id: int, configuration: Optional[Dict[str, Any]] = None) -> AgentTypeMemoryAssociation:
        """Add a memory configuration to an agent type."""
        try:
            association = AgentTypeMemoryAssociation(
                agent_type_id=agent_type_id,
                memory_config_id=memory_config_id,
                configuration=configuration
            )
            self.db.add(association)
            self.db.commit()
            self.db.refresh(association)
            return association
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to add memory config {memory_config_id} to agent type {agent_type_id}: {e}")
            raise

    def add_knowledge_to_agent_type(self, agent_type_id: int, knowledge_base_id: int, configuration: Optional[Dict[str, Any]] = None) -> AgentTypeKnowledgeAssociation:
        """Add a knowledge base to an agent type."""
        try:
            association = AgentTypeKnowledgeAssociation(
                agent_type_id=agent_type_id,
                knowledge_base_id=knowledge_base_id,
                configuration=configuration
            )
            self.db.add(association)
            self.db.commit()
            self.db.refresh(association)
            return association
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to add knowledge base {knowledge_base_id} to agent type {agent_type_id}: {e}")
            raise
