"""
Dynamic Tool Loader

This module provides functionality to dynamically load and register tools
from database configuration, enabling runtime tool management and registration.
"""

import importlib
import inspect
from typing import Dict, Any, Optional, List, Type
from loguru import logger

from .base import Tool, ToolSpec
from .registry import ToolRegistry
from ..database.repositories import ToolRegistryRepository
from ..database.models import ToolRegistry as ToolRegistryModel
from ..observability.logging import get_logger

logger = get_logger(__name__)


class DynamicToolLoader:
    """
    Dynamic tool loader for registering tools from database configuration.
    
    This class handles the dynamic loading and registration of tools based on
    database configuration, allowing for runtime tool management.
    """
    
    def __init__(self, registry: ToolRegistry):
        """
        Initialize the dynamic tool loader.
        
        Args:
            registry: Tool registry instance to register loaded tools
        """
        self.registry = registry
        self.loaded_tools: Dict[str, Tool] = {}
        
    def load_tool_from_config(self, tool_config: ToolRegistryModel) -> Optional[Tool]:
        """
        Load a tool from database configuration.
        
        Args:
            tool_config: Tool configuration from database
            
        Returns:
            Tool: Loaded tool instance or None if loading failed
        """
        try:
            # Import the module
            module = importlib.import_module(tool_config.module_path)
            
            # Get the tool class
            tool_class = getattr(module, tool_config.class_name)
            
            # Validate that it's a proper tool
            if not self._is_valid_tool_class(tool_class):
                logger.error(f"Invalid tool class: {tool_config.class_name}")
                return None
            
            # Create tool spec
            tool_spec = ToolSpec(
                name=tool_config.name,
                description=tool_config.description or "",
                input_schema=tool_config.parameters_schema or {},
                output_schema=tool_config.output_schema or {}
            )
            
            # Create tool instance
            if hasattr(tool_class, 'from_config'):
                # Tool has custom configuration method
                tool_instance = tool_class.from_config(tool_config.configuration or {})
            else:
                # Standard tool instantiation
                tool_instance = tool_class()
            
            # Create Tool wrapper
            tool = Tool(tool_spec, tool_instance)
            
            logger.info(f"Successfully loaded tool: {tool_config.name}")
            return tool
            
        except ImportError as e:
            logger.error(f"Failed to import module {tool_config.module_path}: {e}")
            return None
        except AttributeError as e:
            logger.error(f"Failed to get class {tool_config.class_name}: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to load tool {tool_config.name}: {e}")
            return None
    
    def _is_valid_tool_class(self, tool_class: Type) -> bool:
        """
        Validate that a class is a proper tool class.
        
        Args:
            tool_class: Class to validate
            
        Returns:
            bool: True if valid tool class
        """
        # Check if class is callable
        if not callable(tool_class):
            return False
        
        # Check if class has required methods
        required_methods = ['__call__']
        for method in required_methods:
            if not hasattr(tool_class, method):
                return False
        
        return True
    
    def load_tools_from_database(self, tool_repo: ToolRegistryRepository) -> int:
        """
        Load all active tools from database and register them.
        
        Args:
            tool_repo: Tool repository instance
            
        Returns:
            int: Number of tools successfully loaded
        """
        try:
            # Get all active tools from database
            active_tools = tool_repo.get_active_tools()
            
            loaded_count = 0
            for tool_config in active_tools:
                # Skip if already loaded
                if tool_config.name in self.loaded_tools:
                    continue
                
                # Load tool
                tool = self.load_tool_from_config(tool_config)
                if tool:
                    # Register tool
                    self.registry.register(tool)
                    self.loaded_tools[tool_config.name] = tool
                    loaded_count += 1
                    
                    logger.info(f"Registered tool: {tool_config.name}")
            
            logger.info(f"Loaded {loaded_count} tools from database")
            return loaded_count
            
        except Exception as e:
            logger.error(f"Failed to load tools from database: {e}")
            return 0
    
    def reload_tool(self, tool_name: str, tool_repo: ToolRegistryRepository) -> bool:
        """
        Reload a specific tool from database configuration.
        
        Args:
            tool_name: Name of tool to reload
            tool_repo: Tool repository instance
            
        Returns:
            bool: True if tool was successfully reloaded
        """
        try:
            # Get tool configuration from database
            tool_config = tool_repo.get_by_name(tool_name)
            if not tool_config:
                logger.error(f"Tool configuration not found: {tool_name}")
                return False
            
            # Load tool
            tool = self.load_tool_from_config(tool_config)
            if not tool:
                return False
            
            # Re-register tool (this will replace existing registration)
            self.registry.register(tool)
            self.loaded_tools[tool_name] = tool
            
            logger.info(f"Reloaded tool: {tool_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to reload tool {tool_name}: {e}")
            return False
    
    def unload_tool(self, tool_name: str) -> bool:
        """
        Unload a tool from registry.
        
        Args:
            tool_name: Name of tool to unload
            
        Returns:
            bool: True if tool was successfully unloaded
        """
        try:
            # Remove from loaded tools
            if tool_name in self.loaded_tools:
                del self.loaded_tools[tool_name]
            
            # Remove from registry (if registry supports removal)
            if hasattr(self.registry, 'unregister'):
                self.registry.unregister(tool_name)
            
            logger.info(f"Unloaded tool: {tool_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unload tool {tool_name}: {e}")
            return False
    
    def get_loaded_tools(self) -> List[str]:
        """
        Get list of currently loaded tool names.
        
        Returns:
            List[str]: List of loaded tool names
        """
        return list(self.loaded_tools.keys())
    
    def is_tool_loaded(self, tool_name: str) -> bool:
        """
        Check if a tool is currently loaded.
        
        Args:
            tool_name: Name of tool to check
            
        Returns:
            bool: True if tool is loaded
        """
        return tool_name in self.loaded_tools


# Global tool loader instance
_tool_loader: Optional[DynamicToolLoader] = None


def get_tool_loader(registry: Optional[ToolRegistry] = None) -> DynamicToolLoader:
    """
    Get the global tool loader instance.
    
    Args:
        registry: Tool registry instance (required for first call)
        
    Returns:
        DynamicToolLoader: Global tool loader instance
    """
    global _tool_loader
    
    if _tool_loader is None:
        if registry is None:
            raise ValueError("Registry is required for first call to get_tool_loader")
        _tool_loader = DynamicToolLoader(registry)
    
    return _tool_loader
