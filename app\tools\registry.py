"""
Tool Registry

This module provides the core tool registry for managing and accessing tools
at runtime. The registry maintains a collection of registered tools and
provides methods for registration, retrieval, and management.
"""

from __future__ import annotations
from typing import Dict, List, Optional
from loguru import logger

from .base import Tool


class ToolRegistry:
    """
    Tool registry for managing registered tools.

    This class maintains a collection of tools that can be executed by agents
    and provides methods for registering, retrieving, and managing tools.
    """

    def __init__(self) -> None:
        """Initialize the tool registry."""
        self._tools: Dict[str, Tool] = {}
        logger.debug("Initialized tool registry")

    def register(self, tool: Tool) -> None:
        """
        Register a tool in the registry.

        Args:
            tool: Tool instance to register
        """
        self._tools[tool.spec.name] = tool
        logger.info(f"Registered tool: {tool.spec.name}")

    def unregister(self, tool_name: str) -> bool:
        """
        Unregister a tool from the registry.

        Args:
            tool_name: Name of tool to unregister

        Returns:
            bool: True if tool was unregistered, False if not found
        """
        if tool_name in self._tools:
            del self._tools[tool_name]
            logger.info(f"Unregistered tool: {tool_name}")
            return True
        return False

    def get(self, name: str) -> Tool:
        """
        Get a tool by name.

        Args:
            name: Tool name

        Returns:
            Tool: Tool instance

        Raises:
            KeyError: If tool is not found
        """
        if name not in self._tools:
            raise KeyError(f"Tool not found: {name}")
        return self._tools[name]

    def list(self) -> List[Tool]:
        """
        Get list of all registered tools.

        Returns:
            List[Tool]: List of registered tools
        """
        return list(self._tools.values())

    def get_tool_names(self) -> List[str]:
        """
        Get list of all registered tool names.

        Returns:
            List[str]: List of tool names
        """
        return list(self._tools.keys())

    def has_tool(self, tool_name: str) -> bool:
        """
        Check if a tool is registered.

        Args:
            tool_name: Tool name to check

        Returns:
            bool: True if tool is registered
        """
        return tool_name in self._tools

    def clear(self) -> None:
        """Clear all registered tools."""
        self._tools.clear()
        logger.info("Cleared all tools from registry")

    def count(self) -> int:
        """
        Get number of registered tools.

        Returns:
            int: Number of registered tools
        """
        return len(self._tools)
