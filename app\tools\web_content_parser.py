"""
Web Content Parser Tool

This tool fetches and parses web content from URLs using two approaches:
1. httpx + trafilatura for static content (fast, efficient)
2. Playwright for dynamic sites with JavaScript (fallback)

The tool automatically detects the best approach and falls back when needed.
"""

import time
import asyncio
from typing import Dict, Any, Optional
from loguru import logger
import httpx

# Try to import trafilatura
try:
    import trafilatura
    TRAFILATURA_AVAILABLE = True
except ImportError:
    TRAFILATURA_AVAILABLE = False
    logger.warning("trafilatura not available. Install with: pip install trafilatura")

# Try to import playwright
try:
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    logger.warning("playwright not available. Install with: pip install playwright")

from .base import Tool, ToolSpec


class WebContentParserTool(Tool):
    """
    Web content parser tool that fetches and extracts clean text from web pages.
    
    Uses httpx + trafilatura for static content and falls back to <PERSON><PERSON>
    for dynamic sites that require JavaScript execution.
    """
    
    def __init__(self):
        """Initialize web content parser tool."""
        # Define tool specification
        self.spec = ToolSpec(
            name="web_content_parser",
            description="Fetch and parse web content from URLs, extracting clean text from static and dynamic sites",
            input_schema={
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "URL to fetch and parse content from",
                        "format": "uri"
                    },
                    "timeout": {
                        "type": "number",
                        "description": "Request timeout in seconds",
                        "default": 30,
                        "minimum": 1,
                        "maximum": 120
                    },
                    "force_playwright": {
                        "type": "boolean",
                        "description": "Force use of Playwright instead of trying static parsing first",
                        "default": False
                    },
                    "include_metadata": {
                        "type": "boolean",
                        "description": "Include page metadata (title, author, date) in response",
                        "default": True
                    }
                },
                "required": ["url"]
            },
            output_schema={
                "type": "object",
                "properties": {
                    "url": {"type": "string"},
                    "content": {"type": "string"},
                    "title": {"type": "string"},
                    "author": {"type": "string"},
                    "date": {"type": "string"},
                    "method_used": {"type": "string"},
                    "success": {"type": "boolean"},
                    "error": {"type": "string"},
                    "processing_time": {"type": "number"}
                }
            }
        )
        
        logger.info("Web content parser tool initialized")
        logger.info(f"Trafilatura available: {TRAFILATURA_AVAILABLE}")
        logger.info(f"Playwright available: {PLAYWRIGHT_AVAILABLE}")
        logger.info("Tool initialization complete - ready for web content parsing")
    
    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute web content parsing.
        
        Args:
            params: Dictionary containing:
                - url (str): URL to fetch and parse
                - timeout (int, optional): Request timeout in seconds (default: 30)
                - force_playwright (bool, optional): Force Playwright usage (default: False)
                - include_metadata (bool, optional): Include metadata (default: True)
        
        Returns:
            Dictionary containing parsed content and metadata
        """
        start_time = time.time()
        
        url = params["url"]
        timeout = params.get("timeout", 30)
        force_playwright = params.get("force_playwright", False)
        include_metadata = params.get("include_metadata", True)
        
        logger.debug(f"Parsing web content from: {url}")
        
        result = {
            "url": url,
            "content": "",
            "title": "",
            "author": "",
            "date": "",
            "method_used": "",
            "success": False,
            "error": "",
            "processing_time": 0.0
        }
        
        try:
            if not force_playwright and TRAFILATURA_AVAILABLE:
                logger.debug("Attempting static parsing with httpx + trafilatura")
                static_result = await asyncio.to_thread(
                    self._parse_static,
                    url,
                    timeout,
                    include_metadata
                )
                
                if static_result["success"] and static_result["content"].strip():
                    result.update(static_result)
                    result["method_used"] = "static"
                    logger.info(f"Successfully parsed content using static method: {len(result['content'])} characters")
                elif PLAYWRIGHT_AVAILABLE:
                    logger.debug("Static parsing failed or returned empty content, trying Playwright")
                    playwright_result = await self._parse_dynamic(url, timeout, include_metadata)
                    result.update(playwright_result)
                    result["method_used"] = "playwright"
                else:
                    result["error"] = "Static parsing failed and Playwright not available"
                    logger.error(result["error"])
            elif PLAYWRIGHT_AVAILABLE:
                logger.debug("Using Playwright for dynamic content parsing")
                playwright_result = await self._parse_dynamic(url, timeout, include_metadata)
                result.update(playwright_result)
                result["method_used"] = "playwright"
            else:
                result["error"] = "Neither trafilatura nor Playwright is available"
                logger.error(result["error"])
        
        except Exception as e:
            result["error"] = f"Unexpected error: {str(e)}"
            logger.error(f"Web content parsing failed: {e}")
        
        finally:
            result["processing_time"] = time.time() - start_time
        
        return result

    def _parse_static(self, url: str, timeout: int, include_metadata: bool) -> Dict[str, Any]:
        """Parse content using httpx + trafilatura (static approach)."""
        result = {
            "content": "",
            "title": "",
            "author": "",
            "date": "",
            "success": False,
            "error": ""
        }
        
        try:
            # Fetch content with httpx
            with httpx.Client(timeout=timeout) as client:
                response = client.get(url, follow_redirects=True)
                response.raise_for_status()
                html_content = response.text
            
            # Extract content with trafilatura
            extracted = trafilatura.extract(
                html_content,
                include_comments=False,
                include_tables=True,
                include_formatting=False,
                favor_precision=True
            )
            
            if extracted:
                result["content"] = extracted.strip()
                result["success"] = True
                
                # Extract metadata if requested
                if include_metadata:
                    metadata = trafilatura.extract_metadata(html_content)
                    if metadata:
                        result["title"] = metadata.title or ""
                        result["author"] = metadata.author or ""
                        result["date"] = metadata.date or ""
            else:
                result["error"] = "Trafilatura failed to extract content"
        
        except httpx.RequestError as e:
            result["error"] = f"HTTP request failed: {str(e)}"
        except httpx.HTTPStatusError as e:
            result["error"] = f"HTTP error {e.response.status_code}: {e.response.reason_phrase}"
        except Exception as e:
            result["error"] = f"Static parsing error: {str(e)}"
        
        return result
    
    async def _parse_dynamic(self, url: str, timeout: int, include_metadata: bool) -> Dict[str, Any]:
        """Parse content using Playwright (dynamic approach)."""
        result = {
            "content": "",
            "title": "",
            "author": "",
            "date": "",
            "success": False,
            "error": ""
        }

        if not PLAYWRIGHT_AVAILABLE:
            result["error"] = "Playwright not available"
            return result

        browser = None
        page = None

        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()

                page.set_default_timeout(timeout * 1000)

                await page.goto(url, wait_until="domcontentloaded")
                await page.wait_for_timeout(2000)

                html_content = await page.content()

                if include_metadata:
                    try:
                        result["title"] = await page.title() or ""
                    except Exception:
                        result["title"] = ""

                if TRAFILATURA_AVAILABLE:
                    extracted = await asyncio.to_thread(
                        trafilatura.extract,
                        html_content,
                        include_comments=False,
                        include_tables=True,
                        include_formatting=False,
                        favor_precision=True
                    )

                    if extracted:
                        result["content"] = extracted.strip()
                        result["success"] = True

                        if include_metadata:
                            metadata = await asyncio.to_thread(
                                trafilatura.extract_metadata,
                                html_content
                            )
                            if metadata:
                                if not result["title"]:
                                    result["title"] = metadata.title or ""
                                result["author"] = metadata.author or ""
                                result["date"] = metadata.date or ""
                    else:
                        result["error"] = "Failed to extract content from rendered HTML"
                else:
                    try:
                        await page.evaluate(
                            """
                            () => {
                                const scripts = document.querySelectorAll('script, style');
                                scripts.forEach(el => el.remove());
                            }
                            """
                        )

                        text_content = await page.evaluate("() => document.body.innerText")
                        if text_content:
                            result["content"] = text_content.strip()
                            result["success"] = True
                        else:
                            result["error"] = "No text content found"
                    except Exception as extraction_error:
                        result["error"] = f"Failed to extract text: {extraction_error}"
        except Exception as e:
            result["error"] = f"Playwright parsing error: {str(e)}"
        finally:
            if page:
                try:
                    await page.close()
                except Exception:
                    pass
            if browser:
                try:
                    await browser.close()
                except Exception:
                    pass

        return result




# Create tool instance
web_content_parser = WebContentParserTool()
