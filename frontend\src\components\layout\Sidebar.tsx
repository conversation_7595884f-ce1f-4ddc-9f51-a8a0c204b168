/**
 * Sidebar Navigation Component
 * 
 * Provides navigation menu with collapsible functionality
 * and active route highlighting.
 */

import { Link, useLocation } from 'react-router-dom'
import {
  Home,
  Bot,
  MessageSquare,
  Brain,
  Settings,
  ChevronLeft,
  ChevronRight,
  Zap,
  Layers
} from 'lucide-react'
import { useAppStore } from '@/stores/appStore'
import { cn } from '@/utils'

const navigationItems = [
  {
    name: 'Dashboard',
    href: '/',
    icon: Home,
  },
  {
    name: 'Agent Types',
    href: '/agent-types',
    icon: Layers,
  },
  {
    name: 'Agents',
    href: '/agents',
    icon: Bot,
  },
  {
    name: 'Tools',
    href: '/tools',
    icon: Zap,
  },
  {
    name: 'Chat',
    href: '/chat',
    icon: MessageSquare,
  },
  {
    name: 'Memory',
    href: '/memory',
    icon: Brain,
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: Settings,
  },
]

export function Sidebar() {
  const location = useLocation()
  const { sidebarOpen, setSidebarOpen } = useAppStore()

  return (
    <div
      className={cn(
        'fixed left-0 top-0 z-50 h-full bg-card border-r border-border transition-all duration-300 ease-in-out',
        sidebarOpen ? 'w-64' : 'w-16'
      )}
    >
      {/* Header */}
      <div className="flex h-16 items-center justify-between px-4 border-b border-border">
        <div className={cn('flex items-center space-x-2', !sidebarOpen && 'justify-center')}>
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            <Zap className="h-5 w-5" />
          </div>
          {sidebarOpen && (
            <div className="flex flex-col">
              <span className="text-sm font-semibold">AgentKit</span>
              <span className="text-xs text-muted-foreground">AI Platform</span>
            </div>
          )}
        </div>
        
        <button
          onClick={() => setSidebarOpen(!sidebarOpen)}
          className="hidden lg:flex h-8 w-8 items-center justify-center rounded-md hover:bg-accent hover:text-accent-foreground transition-colors"
        >
          {sidebarOpen ? (
            <ChevronLeft className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 p-2">
        {navigationItems.map((item) => {
          const isActive = location.pathname === item.href || 
            (item.href !== '/' && location.pathname.startsWith(item.href))
          
          return (
            <Link
              key={item.name}
              to={item.href}
              className={cn(
                'flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors',
                isActive
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground',
                !sidebarOpen && 'justify-center'
              )}
              title={!sidebarOpen ? item.name : undefined}
            >
              <item.icon className="h-5 w-5 flex-shrink-0" />
              {sidebarOpen && <span>{item.name}</span>}
            </Link>
          )
        })}
      </nav>

      {/* Footer */}
      <div className="border-t border-border p-4">
        <div className={cn('flex items-center space-x-3', !sidebarOpen && 'justify-center')}>
          <div className="h-8 w-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600" />
          {sidebarOpen && (
            <div className="flex flex-col">
              <span className="text-sm font-medium">User</span>
              <span className="text-xs text-muted-foreground"><EMAIL></span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

