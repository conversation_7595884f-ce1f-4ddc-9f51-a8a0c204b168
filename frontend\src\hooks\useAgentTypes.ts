/**
 * useAgentTypes Hook
 * 
 * Custom React hook for managing agent types state and operations.
 * Provides CRUD operations, loading states, and error handling.
 */

import { useState, useEffect, useCallback } from 'react';
import { 
  agentTypesService, 
  AgentType, 
  AgentTypeCreate, 
  AgentTypeUpdate,
  Organization 
} from '../services/agentTypesService';

interface UseAgentTypesState {
  agentTypes: AgentType[];
  organizations: Organization[];
  categories: string[];
  loading: boolean;
  error: string | null;
  currentOrganization: Organization | null;
}

interface UseAgentTypesActions {
  // Organization management
  loadOrganizations: () => Promise<void>;
  setCurrentOrganization: (org: Organization) => void;
  
  // Agent types management
  loadAgentTypes: (category?: string) => Promise<void>;
  loadCategories: () => Promise<void>;
  createAgentType: (data: AgentTypeCreate) => Promise<AgentType>;
  updateAgentType: (id: number, data: AgentTypeUpdate) => Promise<AgentType>;
  deleteAgentType: (id: number) => Promise<void>;
  getAgentType: (id: number) => Promise<AgentType>;
  
  // Utility functions
  clearError: () => void;
  refresh: () => Promise<void>;
}

export function useAgentTypes(): UseAgentTypesState & UseAgentTypesActions {
  const [state, setState] = useState<UseAgentTypesState>({
    agentTypes: [],
    organizations: [],
    categories: [],
    loading: false,
    error: null,
    currentOrganization: null,
  });

  // Load organizations
  const loadOrganizations = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const response = await agentTypesService.listOrganizations();
      
      setState(prev => ({
        ...prev,
        organizations: response.items,
        currentOrganization: prev.currentOrganization || response.items[0] || null,
        loading: false,
      }));

      // Set default organization if none selected
      if (!state.currentOrganization && response.items.length > 0) {
        agentTypesService.setOrganization(response.items[0].id);
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load organizations',
        loading: false,
      }));
    }
  }, [state.currentOrganization]);

  // Set current organization
  const setCurrentOrganization = useCallback((org: Organization) => {
    setState(prev => ({ ...prev, currentOrganization: org }));
    agentTypesService.setOrganization(org.id);
  }, []);

  // Load agent types
  const loadAgentTypes = useCallback(async (category?: string) => {
    try {
      console.log('useAgentTypes: Loading agent types...', { category });
      setState(prev => ({ ...prev, loading: true, error: null }));
      const response = await agentTypesService.listAgentTypes(0, 1000, category);
      console.log('useAgentTypes: Agent types loaded:', response);

      setState(prev => ({
        ...prev,
        agentTypes: response.items,
        loading: false,
      }));
    } catch (error) {
      console.error('useAgentTypes: Failed to load agent types:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load agent types',
        loading: false,
      }));
    }
  }, []);

  // Load categories
  const loadCategories = useCallback(async () => {
    try {
      const categories = await agentTypesService.listCategories();
      setState(prev => ({ ...prev, categories }));
    } catch (error) {
      console.error('Failed to load categories:', error);
      // Don't set error state for categories as it's not critical
    }
  }, []);

  // Create agent type
  const createAgentType = useCallback(async (data: AgentTypeCreate): Promise<AgentType> => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const newAgentType = await agentTypesService.createAgentType(data);
      
      setState(prev => ({
        ...prev,
        agentTypes: [...prev.agentTypes, newAgentType],
        loading: false,
      }));
      
      return newAgentType;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to create agent type',
        loading: false,
      }));
      throw error;
    }
  }, []);

  // Update agent type
  const updateAgentType = useCallback(async (id: number, data: AgentTypeUpdate): Promise<AgentType> => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const updatedAgentType = await agentTypesService.updateAgentType(id, data);
      
      setState(prev => ({
        ...prev,
        agentTypes: prev.agentTypes.map(at => 
          at.id === id ? updatedAgentType : at
        ),
        loading: false,
      }));
      
      return updatedAgentType;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to update agent type',
        loading: false,
      }));
      throw error;
    }
  }, []);

  // Delete agent type
  const deleteAgentType = useCallback(async (id: number): Promise<void> => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      await agentTypesService.deleteAgentType(id);
      
      setState(prev => ({
        ...prev,
        agentTypes: prev.agentTypes.filter(at => at.id !== id),
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to delete agent type',
        loading: false,
      }));
      throw error;
    }
  }, []);

  // Get single agent type
  const getAgentType = useCallback(async (id: number): Promise<AgentType> => {
    try {
      return await agentTypesService.getAgentType(id);
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to get agent type',
      }));
      throw error;
    }
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Refresh all data
  const refresh = useCallback(async () => {
    await Promise.all([
      loadOrganizations(),
      loadAgentTypes(),
      loadCategories(),
    ]);
  }, [loadOrganizations, loadAgentTypes, loadCategories]);

  // Load initial data
  useEffect(() => {
    console.log('useAgentTypes: Loading initial data...');
    refresh().catch(error => {
      console.error('useAgentTypes: Failed to load initial data:', error);
    });
  }, []);

  return {
    // State
    ...state,
    
    // Actions
    loadOrganizations,
    setCurrentOrganization,
    loadAgentTypes,
    loadCategories,
    createAgentType,
    updateAgentType,
    deleteAgentType,
    getAgentType,
    clearError,
    refresh,
  };
}
