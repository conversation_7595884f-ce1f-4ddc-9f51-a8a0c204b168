/**
 * Agent Types Page
 * 
 * This page displays and manages agent types in a multi-tenant environment.
 * Users can view, create, edit, and delete agent types organized by categories.
 */

import React, { useState, useEffect } from 'react';
import { useAgentTypes } from '../hooks/useAgentTypes';
import { AgentType } from '../services/agentTypesService';

interface AgentTypesPageProps {}

const AgentTypesPage: React.FC<AgentTypesPageProps> = () => {
  const {
    agentTypes,
    organizations,
    categories,
    loading,
    error,
    currentOrganization,
    setCurrentOrganization,
    loadAgentTypes,
    createAgentType,
    updateAgentType,
    deleteAgentType,
    clearError,
    refresh
  } = useAgentTypes();

  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingAgentType, setEditingAgentType] = useState<AgentType | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Filter agent types based on search and category
  const filteredAgentTypes = agentTypes.filter(agentType => {
    const matchesSearch = agentType.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (agentType.description?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false);
    const matchesCategory = !selectedCategory || agentType.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Group agent types by category
  const groupedAgentTypes = filteredAgentTypes.reduce((acc, agentType) => {
    const category = agentType.category || 'Uncategorized';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(agentType);
    return acc;
  }, {} as Record<string, AgentType[]>);

  const handleCreateAgentType = () => {
    setShowCreateModal(true);
  };

  const handleEditAgentType = (agentType: AgentType) => {
    setEditingAgentType(agentType);
  };

  const handleDeleteAgentType = async (agentType: AgentType) => {
    if (window.confirm(`Are you sure you want to delete "${agentType.name}"?`)) {
      try {
        await deleteAgentType(agentType.id);
      } catch (error) {
        console.error('Failed to delete agent type:', error);
      }
    }
  };

  const handleCategoryFilter = (category: string) => {
    setSelectedCategory(category === selectedCategory ? '' : category);
  };

  if (loading && agentTypes.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Agent Types</h1>
            <p className="text-gray-600 mt-2">
              Manage and organize your agent types by category
            </p>
          </div>
          <button
            onClick={handleCreateAgentType}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
          >
            Create Agent Type
          </button>
        </div>

        {/* Organization Selector */}
        {organizations.length > 1 && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Organization
            </label>
            <select
              value={currentOrganization?.id || ''}
              onChange={(e) => {
                const org = organizations.find(o => o.id === parseInt(e.target.value));
                if (org) setCurrentOrganization(org);
              }}
              className="border border-gray-300 rounded-md px-3 py-2 bg-white"
            >
              {organizations.map(org => (
                <option key={org.id} value={org.id}>
                  {org.name}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search agent types..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={() => setSelectedCategory('')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                !selectedCategory
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              All Categories
            </button>
            {categories.map(category => (
              <button
                key={category}
                onClick={() => handleCategoryFilter(category)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  selectedCategory === category
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex justify-between items-center">
            <p className="text-red-800">{error}</p>
            <button
              onClick={clearError}
              className="text-red-600 hover:text-red-800"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* Agent Types Grid */}
      {Object.keys(groupedAgentTypes).length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">🤖</div>
          <h3 className="text-xl font-medium text-gray-900 mb-2">No Agent Types Found</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || selectedCategory
              ? 'No agent types match your current filters.'
              : 'Get started by creating your first agent type.'}
          </p>
          {!searchTerm && !selectedCategory && (
            <button
              onClick={handleCreateAgentType}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
            >
              Create Agent Type
            </button>
          )}
        </div>
      ) : (
        <div className="space-y-8">
          {Object.entries(groupedAgentTypes).map(([category, categoryAgentTypes]) => (
            <div key={category}>
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">
                  {categoryAgentTypes.length}
                </span>
                {category}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {categoryAgentTypes.map(agentType => (
                  <AgentTypeCard
                    key={agentType.id}
                    agentType={agentType}
                    onEdit={() => handleEditAgentType(agentType)}
                    onDelete={() => handleDeleteAgentType(agentType)}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modals would go here */}
      {/* TODO: Implement CreateAgentTypeModal and EditAgentTypeModal */}
    </div>
  );
};

// Agent Type Card Component
interface AgentTypeCardProps {
  agentType: AgentType;
  onEdit: () => void;
  onDelete: () => void;
}

const AgentTypeCard: React.FC<AgentTypeCardProps> = ({ agentType, onEdit, onDelete }) => {
  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{agentType.name}</h3>
          {agentType.description && (
            <p className="text-gray-600 text-sm mb-3 line-clamp-2">{agentType.description}</p>
          )}
        </div>
        <div className="flex space-x-2 ml-4">
          <button
            onClick={onEdit}
            className="text-blue-600 hover:text-blue-800 p-1"
            title="Edit"
          >
            ✏️
          </button>
          <button
            onClick={onDelete}
            className="text-red-600 hover:text-red-800 p-1"
            title="Delete"
          >
            🗑️
          </button>
        </div>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500">Category:</span>
          <span className="font-medium">{agentType.category || 'Uncategorized'}</span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500">Status:</span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            agentType.is_active
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          }`}>
            {agentType.is_active ? 'Active' : 'Inactive'}
          </span>
        </div>
        {agentType.is_system && (
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-500">Type:</span>
            <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              System
            </span>
          </div>
        )}
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200">
        <p className="text-xs text-gray-500">
          Created: {new Date(agentType.created_at).toLocaleDateString()}
        </p>
      </div>
    </div>
  );
};

export default AgentTypesPage;
