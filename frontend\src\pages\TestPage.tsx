/**
 * Test Page for API Debugging
 */

import React, { useState } from 'react';
import { agentTypesService } from '../services/agentTypesService';

const TestPage: React.FC = () => {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testOrganizations = async () => {
    setLoading(true);
    try {
      const response = await agentTypesService.listOrganizations();
      setResult(JSON.stringify(response, null, 2));
    } catch (error) {
      setResult(`Error: ${error}`);
    }
    setLoading(false);
  };

  const testAgentTypes = async () => {
    setLoading(true);
    try {
      const response = await agentTypesService.listAgentTypes();
      setResult(JSON.stringify(response, null, 2));
    } catch (error) {
      setResult(`Error: ${error}`);
    }
    setLoading(false);
  };

  const testCategories = async () => {
    setLoading(true);
    try {
      const response = await agentTypesService.listCategories();
      setResult(JSON.stringify(response, null, 2));
    } catch (error) {
      setResult(`Error: ${error}`);
    }
    setLoading(false);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">API Test Page</h1>
      
      <div className="space-y-4 mb-8">
        <button
          onClick={testOrganizations}
          disabled={loading}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded mr-4"
        >
          Test Organizations
        </button>
        
        <button
          onClick={testAgentTypes}
          disabled={loading}
          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded mr-4"
        >
          Test Agent Types
        </button>
        
        <button
          onClick={testCategories}
          disabled={loading}
          className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded mr-4"
        >
          Test Categories
        </button>
      </div>

      {loading && (
        <div className="text-blue-600 mb-4">Loading...</div>
      )}

      <div className="bg-gray-100 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Result:</h2>
        <pre className="whitespace-pre-wrap text-sm">{result}</pre>
      </div>
    </div>
  );
};

export default TestPage;
