/**
 * Tools Management Page
 * 
 * Comprehensive tool management interface for viewing, testing, and creating tools.
 */

import { useState, useEffect } from 'react'
import toast from 'react-hot-toast'
import {
  Zap,
  Plus,
  Play,
  Eye,
  Code,
  Settings,
  Search,
  Filter,
  Download,
  Upload,
  Trash2,
  Edit,
  Copy,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Power,
  PowerOff
} from 'lucide-react'

import { toolsApi } from '@/services/api'

interface ToolSpec {
  name: string
  description: string
  input_schema: Record<string, any>
  output_schema: Record<string, any>
  category?: string
  version?: string
  author?: string
}

interface Tool {
  id: string
  spec: ToolSpec
  status: 'active' | 'inactive' | 'error'
  last_used?: string
  usage_count?: number
}

interface ToolExecution {
  id: string
  tool_name: string
  input: Record<string, any>
  output: Record<string, any>
  success: boolean
  execution_time: number
  timestamp: string
  error?: string
}

export function ToolsPage() {
  const [tools, setTools] = useState<Tool[]>([])
  const [selectedTool, setSelectedTool] = useState<Tool | null>(null)
  const [activeTab, setActiveTab] = useState<'list' | 'spec' | 'test' | 'create'>('list')
  const [searchQuery, setSearchQuery] = useState('')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [categories, setCategories] = useState<string[]>(['all'])
  const [testInput, setTestInput] = useState<string>('{}')
  const [testOutput, setTestOutput] = useState<ToolExecution | null>(null)
  const [isExecuting, setIsExecuting] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [newTool, setNewTool] = useState<Partial<ToolSpec>>({
    name: '',
    description: '',
    input_schema: {},
    output_schema: {},
    category: 'general'
  })

  // Load tools from API
  useEffect(() => {
    loadTools()
    loadCategories()
  }, [])

  const loadTools = async () => {
    try {
      const response = await toolsApi.getTools(
        categoryFilter === 'all' ? undefined : categoryFilter
      )
      setTools(response.data || [])
    } catch (error) {
      console.error('Failed to load tools:', error)
      toast.error('Failed to load tools')
    }
  }

  const loadCategories = async () => {
    try {
      const response = await toolsApi.getCategories()
      const apiCategories = response.data || []
      setCategories(['all', ...apiCategories])
    } catch (error) {
      console.error('Failed to load categories:', error)
    }
  }

  const filteredTools = tools.filter(tool => {
    const matchesSearch = tool.spec.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         tool.spec.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = categoryFilter === 'all' || tool.spec.category === categoryFilter
    return matchesSearch && matchesCategory
  })

  const handleTestTool = async () => {
    if (!selectedTool) return

    setIsExecuting(true)
    try {
      const input = JSON.parse(testInput)

      const response = await toolsApi.executeTool(selectedTool.spec.name, input)
      const result = response.data

      setTestOutput({
        id: result.id,
        tool_name: result.tool_name,
        input: result.input,
        output: result.output,
        success: result.success,
        execution_time: result.execution_time,
        timestamp: result.timestamp,
        error: result.error
      })

      if (result.success) {
        toast.success('Tool executed successfully')
      } else {
        toast.error(`Tool execution failed: ${result.error}`)
      }

    } catch (error) {
      console.error('Tool execution error:', error)
      toast.error('Failed to execute tool')

      setTestOutput({
        id: Date.now().toString(),
        tool_name: selectedTool.spec.name,
        input: JSON.parse(testInput || '{}'),
        output: {},
        success: false,
        execution_time: 0,
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setIsExecuting(false)
    }
  }

  const handleCreateTool = async () => {
    try {
      if (!newTool.name || !newTool.description) {
        toast.error('Name and description are required')
        return
      }

      await toolsApi.createTool({
        name: newTool.name,
        description: newTool.description,
        input_schema: newTool.input_schema || {},
        output_schema: newTool.output_schema || {},
        category: newTool.category || 'general',
        version: newTool.version,
        author: newTool.author
      })

      toast.success('Tool created successfully')

      // Reset form
      setNewTool({
        name: '',
        description: '',
        input_schema: {},
        output_schema: {},
        category: 'general'
      })

      // Refresh tools list
      await loadTools()
      setActiveTab('list')

    } catch (error) {
      console.error('Failed to create tool:', error)
      toast.error('Failed to create tool')
    }
  }

  const handleRegisterTool = async (toolName: string) => {
    try {
      await toolsApi.registerTool(toolName)
      toast.success(`Tool '${toolName}' registered successfully`)
      await loadTools()
    } catch (error) {
      console.error('Failed to register tool:', error)
      toast.error('Failed to register tool')
    }
  }

  const handleUnregisterTool = async (toolName: string) => {
    try {
      await toolsApi.unregisterTool(toolName)
      toast.success(`Tool '${toolName}' unregistered successfully`)
      await loadTools()
    } catch (error) {
      console.error('Failed to unregister tool:', error)
      toast.error('Failed to unregister tool')
    }
  }

  const handleReloadAllTools = async () => {
    try {
      const response = await toolsApi.reloadAllTools()
      const result = response.data
      toast.success(`Reloaded ${result.loaded_count} tools`)
      await loadTools()
    } catch (error) {
      console.error('Failed to reload tools:', error)
      toast.error('Failed to reload tools')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Tools</h1>
          <p className="text-muted-foreground mt-1">
            Manage and test your AI agent tools
          </p>
        </div>
        <button
          onClick={() => setActiveTab('create')}
          className="flex items-center space-x-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
        >
          <Plus className="h-4 w-4" />
          <span>Create Tool</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="card p-4">
          <div className="flex items-center space-x-2">
            <Zap className="h-5 w-5 text-primary" />
            <span className="text-sm font-medium">Total Tools</span>
          </div>
          <p className="text-2xl font-bold mt-2">{tools.length}</p>
        </div>
        <div className="card p-4">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            <span className="text-sm font-medium">Active</span>
          </div>
          <p className="text-2xl font-bold mt-2">{tools.filter(t => t.status === 'active').length}</p>
        </div>
        <div className="card p-4">
          <div className="flex items-center space-x-2">
            <Play className="h-5 w-5 text-blue-500" />
            <span className="text-sm font-medium">Total Executions</span>
          </div>
          <p className="text-2xl font-bold mt-2">{tools.reduce((sum, t) => sum + (t.usage_count || 0), 0)}</p>
        </div>
        <div className="card p-4">
          <div className="flex items-center space-x-2">
            <Settings className="h-5 w-5 text-orange-500" />
            <span className="text-sm font-medium">Categories</span>
          </div>
          <p className="text-2xl font-bold mt-2">{categories.length - 1}</p>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Tools List */}
        <div className="lg:col-span-1 space-y-4">
          {/* Search and Filter */}
          <div className="space-y-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search tools..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>

          {/* Tools List */}
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {filteredTools.map((tool) => (
              <div
                key={tool.id}
                onClick={() => {
                  setSelectedTool(tool)
                  setActiveTab('spec')
                }}
                className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                  selectedTool?.id === tool.id
                    ? 'border-primary bg-primary/5'
                    : 'border-border hover:border-primary/50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">{tool.spec.name}</h3>
                  <div className={`w-2 h-2 rounded-full ${
                    tool.status === 'active' ? 'bg-green-500' :
                    tool.status === 'inactive' ? 'bg-yellow-500' : 'bg-red-500'
                  }`} />
                </div>
                <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                  {tool.spec.description}
                </p>
                <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                  <span>{tool.spec.category}</span>
                  <span>{tool.usage_count || 0} uses</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Main Panel */}
        <div className="lg:col-span-2">
          {selectedTool ? (
            <div className="card">
              {/* Tabs */}
              <div className="border-b border-border">
                <nav className="flex space-x-8 px-6">
                  {[
                    { id: 'spec', name: 'Specification', icon: Eye },
                    { id: 'test', name: 'Test Tool', icon: Play },
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id as any)}
                      className={`flex items-center space-x-2 py-4 border-b-2 transition-colors ${
                        activeTab === tab.id
                          ? 'border-primary text-primary'
                          : 'border-transparent text-muted-foreground hover:text-foreground'
                      }`}
                    >
                      <tab.icon className="h-4 w-4" />
                      <span>{tab.name}</span>
                    </button>
                  ))}
                </nav>
              </div>

              {/* Tab Content */}
              <div className="p-6">
                {activeTab === 'spec' && (
                  <div className="space-y-6">
                    <div>
                      <h2 className="text-xl font-semibold">{selectedTool.spec.name}</h2>
                      <p className="text-muted-foreground mt-1">{selectedTool.spec.description}</p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="font-medium mb-3">Input Schema</h3>
                        <pre className="bg-muted p-4 rounded-lg text-sm overflow-auto">
                          {JSON.stringify(selectedTool.spec.input_schema, null, 2)}
                        </pre>
                      </div>
                      <div>
                        <h3 className="font-medium mb-3">Output Schema</h3>
                        <pre className="bg-muted p-4 rounded-lg text-sm overflow-auto">
                          {JSON.stringify(selectedTool.spec.output_schema, null, 2)}
                        </pre>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'test' && (
                  <div className="space-y-6">
                    <div>
                      <h2 className="text-xl font-semibold">Test {selectedTool.spec.name}</h2>
                      <p className="text-muted-foreground mt-1">
                        Enter input parameters and execute the tool
                      </p>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">Input Parameters (JSON)</label>
                        <textarea
                          value={testInput}
                          onChange={(e) => setTestInput(e.target.value)}
                          rows={8}
                          className="w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary font-mono text-sm"
                          placeholder="Enter JSON input parameters..."
                        />
                      </div>

                      <button
                        onClick={handleTestTool}
                        disabled={isExecuting}
                        className="flex items-center space-x-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
                      >
                        <Play className="h-4 w-4" />
                        <span>{isExecuting ? 'Executing...' : 'Execute Tool'}</span>
                      </button>

                      {testOutput && (
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2">
                            {testOutput.success ? (
                              <CheckCircle className="h-5 w-5 text-green-500" />
                            ) : (
                              <XCircle className="h-5 w-5 text-red-500" />
                            )}
                            <span className="font-medium">
                              {testOutput.success ? 'Success' : 'Error'}
                            </span>
                            <span className="text-sm text-muted-foreground">
                              ({testOutput.execution_time}s)
                            </span>
                          </div>

                          <div>
                            <label className="block text-sm font-medium mb-2">Output</label>
                            <pre className="bg-muted p-4 rounded-lg text-sm overflow-auto">
                              {JSON.stringify(testOutput.output, null, 2)}
                            </pre>
                          </div>

                          {testOutput.error && (
                            <div>
                              <label className="block text-sm font-medium mb-2 text-red-500">Error</label>
                              <pre className="bg-red-50 border border-red-200 p-4 rounded-lg text-sm text-red-700">
                                {testOutput.error}
                              </pre>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : activeTab === 'create' ? (
            <div className="card p-6">
              <div className="space-y-6">
                <div>
                  <h2 className="text-xl font-semibold">Create New Tool</h2>
                  <p className="text-muted-foreground mt-1">
                    Define a new tool specification
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Tool Name</label>
                    <input
                      type="text"
                      value={newTool.name || ''}
                      onChange={(e) => setNewTool(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="e.g., my_custom_tool"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Category</label>
                    <input
                      type="text"
                      value={newTool.category || ''}
                      onChange={(e) => setNewTool(prev => ({ ...prev, category: e.target.value }))}
                      className="w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="e.g., utility, conversion"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Description</label>
                  <textarea
                    value={newTool.description || ''}
                    onChange={(e) => setNewTool(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                    className="w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Describe what this tool does..."
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Input Schema (JSON)</label>
                    <textarea
                      value={JSON.stringify(newTool.input_schema, null, 2)}
                      onChange={(e) => {
                        try {
                          const schema = JSON.parse(e.target.value)
                          setNewTool(prev => ({ ...prev, input_schema: schema }))
                        } catch {}
                      }}
                      rows={8}
                      className="w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary font-mono text-sm"
                      placeholder="Define input parameters..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Output Schema (JSON)</label>
                    <textarea
                      value={JSON.stringify(newTool.output_schema, null, 2)}
                      onChange={(e) => {
                        try {
                          const schema = JSON.parse(e.target.value)
                          setNewTool(prev => ({ ...prev, output_schema: schema }))
                        } catch {}
                      }}
                      rows={8}
                      className="w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary font-mono text-sm"
                      placeholder="Define output structure..."
                    />
                  </div>
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={handleCreateTool}
                    className="flex items-center space-x-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
                  >
                    <Plus className="h-4 w-4" />
                    <span>Create Tool</span>
                  </button>
                  <button
                    onClick={() => setActiveTab('list')}
                    className="px-4 py-2 border border-border rounded-lg hover:bg-accent transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="card p-12 text-center">
              <Zap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Select a Tool</h3>
              <p className="text-muted-foreground">
                Choose a tool from the list to view its specification and test it
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}