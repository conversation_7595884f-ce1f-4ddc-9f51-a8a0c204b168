/**
 * Agent Types Service
 * 
 * This service handles all API interactions for agent types management
 * including CRUD operations and organization-specific filtering.
 */

import { api } from './api';

export interface AgentType {
  id: number;
  name: string;
  description?: string;
  category?: string;
  configuration?: Record<string, any>;
  system_prompt?: string;
  instructions?: string;
  is_active: boolean;
  is_system: boolean;
  organization_id: number;
  created_at: string;
  updated_at: string;
}

export interface AgentTypeCreate {
  name: string;
  description?: string;
  category?: string;
  configuration?: Record<string, any>;
  system_prompt?: string;
  instructions?: string;
  is_active?: boolean;
  organization_id: number;
}

export interface AgentTypeUpdate {
  name?: string;
  description?: string;
  category?: string;
  configuration?: Record<string, any>;
  system_prompt?: string;
  instructions?: string;
  is_active?: boolean;
}

export interface AgentTypeListResponse {
  total: number;
  page: number;
  size: number;
  pages: number;
  items: AgentType[];
}

export interface Organization {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface OrganizationListResponse {
  total: number;
  page: number;
  size: number;
  pages: number;
  items: Organization[];
}

class AgentTypesService {
  private organizationId: number = 1; // Default organization for now

  /**
   * Set the current organization context
   */
  setOrganization(organizationId: number) {
    this.organizationId = organizationId;
  }

  /**
   * Get headers with organization context
   */
  private getHeaders() {
    return {
      'X-Organization-ID': this.organizationId.toString(),
      'Content-Type': 'application/json',
    };
  }

  /**
   * List all organizations
   */
  async listOrganizations(skip = 0, limit = 100): Promise<OrganizationListResponse> {
    const response = await api.get(`/organizations/`, {
      params: { skip, limit }
    });
    return response.data;
  }

  /**
   * Get a specific organization
   */
  async getOrganization(id: number): Promise<Organization> {
    const response = await api.get(`/organizations/${id}`);
    return response.data;
  }

  /**
   * List agent types for the current organization
   */
  async listAgentTypes(skip = 0, limit = 100, category?: string): Promise<AgentTypeListResponse> {
    const params: Record<string, any> = { skip, limit };
    if (category) {
      params.category = category;
    }

    const response = await api.get(`/agent-types/`, {
      params,
      headers: this.getHeaders(),
    });
    return response.data;
  }

  /**
   * Get a specific agent type
   */
  async getAgentType(id: number): Promise<AgentType> {
    const response = await api.get(`/agent-types/${id}`, {
      headers: this.getHeaders(),
    });
    return response.data;
  }

  /**
   * Create a new agent type
   */
  async createAgentType(data: AgentTypeCreate): Promise<AgentType> {
    const response = await api.post('/agent-types/', data, {
      headers: this.getHeaders(),
    });
    return response.data;
  }

  /**
   * Update an existing agent type
   */
  async updateAgentType(id: number, data: AgentTypeUpdate): Promise<AgentType> {
    const response = await api.put(`/agent-types/${id}`, data, {
      headers: this.getHeaders(),
    });
    return response.data;
  }

  /**
   * Delete an agent type
   */
  async deleteAgentType(id: number): Promise<void> {
    await api.delete(`/agent-types/${id}`, {
      headers: this.getHeaders(),
    });
  }

  /**
   * List active agent types
   */
  async listActiveAgentTypes(): Promise<AgentType[]> {
    const response = await api.get('/agent-types/active', {
      headers: this.getHeaders(),
    });
    return response.data;
  }

  /**
   * List agent type categories
   */
  async listCategories(): Promise<string[]> {
    const response = await api.get('/agent-types/categories', {
      headers: this.getHeaders(),
    });
    return response.data;
  }

  /**
   * Get agent types by category
   */
  async getAgentTypesByCategory(category: string): Promise<AgentType[]> {
    const response = await this.listAgentTypes(0, 1000, category);
    return response.items;
  }
}

// Export singleton instance
export const agentTypesService = new AgentTypesService();

// Export default
export default agentTypesService;
