/**
 * API Service Layer
 * 
 * Centralized HTTP client with error handling, request/response interceptors,
 * and type safety for all API communications.
 */

import axios, { AxiosInstance, AxiosError, AxiosResponse } from 'axios'
import toast from 'react-hot-toast'
import type { ApiResponse } from '@/types'

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'
const API_TIMEOUT = 30000 // 30 seconds

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add correlation ID for request tracking
    const correlationId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    config.headers['X-Correlation-ID'] = correlationId
    
    // Log request in development
    if (import.meta.env.DEV) {
      console.log(`🚀 API Request [${correlationId}]:`, {
        method: config.method?.toUpperCase(),
        url: config.url,
        data: config.data,
      })
    }
    
    return config
  },
  (error) => {
    console.error('❌ Request Error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    const correlationId = response.config.headers['X-Correlation-ID']
    
    // Log response in development
    if (import.meta.env.DEV) {
      console.log(`✅ API Response [${correlationId}]:`, {
        status: response.status,
        data: response.data,
      })
    }
    
    return response
  },
  (error: AxiosError) => {
    const correlationId = error.config?.headers?.['X-Correlation-ID']
    
    // Log error
    console.error(`❌ API Error [${correlationId}]:`, {
      status: error.response?.status,
      message: error.message,
      data: error.response?.data,
    })
    
    // Handle different error types
    if (error.response) {
      // Server responded with error status
      const status = error.response.status
      const message = getErrorMessage(error.response.data)
      
      switch (status) {
        case 400:
          toast.error(`Bad Request: ${message}`)
          break
        case 401:
          toast.error('Unauthorized: Please check your credentials')
          break
        case 403:
          toast.error('Forbidden: You do not have permission')
          break
        case 404:
          toast.error('Not Found: The requested resource was not found')
          break
        case 422:
          toast.error(`Validation Error: ${message}`)
          break
        case 500:
          toast.error('Server Error: Please try again later')
          break
        default:
          toast.error(`Error ${status}: ${message}`)
      }
    } else if (error.request) {
      // Network error
      toast.error('Network Error: Please check your connection')
    } else {
      // Other error
      toast.error(`Error: ${error.message}`)
    }
    
    return Promise.reject(error)
  }
)

// Helper function to extract error message
function getErrorMessage(errorData: any): string {
  if (typeof errorData === 'string') {
    return errorData
  }
  
  if (errorData?.detail) {
    if (typeof errorData.detail === 'string') {
      return errorData.detail
    }
    if (Array.isArray(errorData.detail)) {
      return errorData.detail.map((d: any) => d.msg || d.message || String(d)).join(', ')
    }
  }
  
  if (errorData?.message) {
    return errorData.message
  }
  
  return 'An unexpected error occurred'
}

// Generic API methods
export const api = {
  // GET request
  async get<T = any>(url: string, config?: { params?: Record<string, any>; headers?: Record<string, string> }): Promise<ApiResponse<T>> {
    try {
      const response = await apiClient.get<T>(url, config)
      return {
        data: response.data,
        status: response.status,
      }
    } catch (error) {
      throw error
    }
  },

  // POST request
  async post<T = any>(url: string, data?: any, config?: { headers?: Record<string, string> }): Promise<ApiResponse<T>> {
    try {
      const response = await apiClient.post<T>(url, data, config)
      return {
        data: response.data,
        status: response.status,
      }
    } catch (error) {
      throw error
    }
  },

  // PUT request
  async put<T = any>(url: string, data?: any, config?: { headers?: Record<string, string> }): Promise<ApiResponse<T>> {
    try {
      const response = await apiClient.put<T>(url, data, config)
      return {
        data: response.data,
        status: response.status,
      }
    } catch (error) {
      throw error
    }
  },

  // PATCH request
  async patch<T = any>(url: string, data?: any, config?: { headers?: Record<string, string> }): Promise<ApiResponse<T>> {
    try {
      const response = await apiClient.patch<T>(url, data, config)
      return {
        data: response.data,
        status: response.status,
      }
    } catch (error) {
      throw error
    }
  },

  // DELETE request
  async delete<T = any>(url: string, config?: { headers?: Record<string, string> }): Promise<ApiResponse<T>> {
    try {
      const response = await apiClient.delete<T>(url, config)
      return {
        data: response.data,
        status: response.status,
      }
    } catch (error) {
      throw error
    }
  },
}

// Health check
export const healthCheck = async (): Promise<boolean> => {
  try {
    const response = await api.get('/health')
    return response.status === 200
  } catch (error) {
    return false
  }
}

// Tool Management API
export const toolsApi = {
  // Get all tools
  async getTools(category?: string, status?: string) {
    const params: Record<string, string> = {}
    if (category) params.category = category
    if (status) params.status = status

    return api.get('/api/tools', { params })
  },

  // Get specific tool
  async getTool(toolName: string) {
    return api.get(`/api/tools/${toolName}`)
  },

  // Create new tool
  async createTool(toolData: {
    name: string
    description: string
    input_schema: Record<string, any>
    output_schema: Record<string, any>
    category?: string
    module_path?: string
    class_name?: string
    version?: string
    author?: string
  }) {
    return api.post('/api/tools', toolData)
  },

  // Update tool
  async updateTool(toolName: string, updateData: {
    description?: string
    input_schema?: Record<string, any>
    output_schema?: Record<string, any>
    category?: string
    version?: string
    author?: string
    is_active?: boolean
  }) {
    return api.put(`/api/tools/${toolName}`, updateData)
  },

  // Execute tool
  async executeTool(toolName: string, parameters: Record<string, any>) {
    return api.post('/api/tools/execute', {
      tool_name: toolName,
      parameters
    })
  },

  // Get tool categories
  async getCategories() {
    return api.get('/api/tools/categories/')
  },

  // Get tool execution history
  async getToolExecutions(toolName: string, limit = 50) {
    return api.get(`/api/tools/${toolName}/executions`, { params: { limit } })
  },

  // Get tool statistics
  async getToolStats(toolName: string) {
    return api.get(`/api/tools/${toolName}/stats`)
  },

  // Register tool
  async registerTool(toolName: string) {
    return api.post(`/api/tools/${toolName}/register`)
  },

  // Unregister tool
  async unregisterTool(toolName: string) {
    return api.post(`/api/tools/${toolName}/unregister`)
  },

  // Reload all tools
  async reloadAllTools() {
    return api.post('/api/tools/reload-all')
  }
}

export default api
