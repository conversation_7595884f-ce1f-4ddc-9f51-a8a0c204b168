/**
 * Tools API Service
 * 
 * Handles all tool-related API operations including listing, testing, and creation.
 */

import apiClient from './api'

export interface ToolSpec {
  name: string
  description: string
  input_schema: Record<string, any>
  output_schema: Record<string, any>
  category?: string
  version?: string
  author?: string
}

export interface Tool {
  id: string
  spec: ToolSpec
  status: 'active' | 'inactive' | 'error'
  last_used?: string
  usage_count?: number
  created_at?: string
  updated_at?: string
}

export interface ToolExecution {
  id: string
  tool_name: string
  input: Record<string, any>
  output: Record<string, any>
  success: boolean
  execution_time: number
  timestamp: string
  error?: string
}

export interface CreateToolRequest {
  name: string
  description: string
  input_schema: Record<string, any>
  output_schema: Record<string, any>
  category?: string
  module_path?: string
  class_name?: string
}

export interface ExecuteToolRequest {
  tool_name: string
  parameters: Record<string, any>
}

class ToolsApiService {
  /**
   * Get all registered tools
   */
  async getTools(): Promise<Tool[]> {
    try {
      const response = await apiClient.get('/api/tools')
      return response.data
    } catch (error) {
      console.error('Failed to fetch tools:', error)
      throw error
    }
  }

  /**
   * Get tool by name
   */
  async getTool(name: string): Promise<Tool> {
    try {
      const response = await apiClient.get(`/api/tools/${name}`)
      return response.data
    } catch (error) {
      console.error(`Failed to fetch tool ${name}:`, error)
      throw error
    }
  }

  /**
   * Execute a tool with given parameters
   */
  async executeTool(request: ExecuteToolRequest): Promise<ToolExecution> {
    try {
      const response = await apiClient.post('/api/tools/execute', request)
      return response.data
    } catch (error) {
      console.error('Failed to execute tool:', error)
      throw error
    }
  }

  /**
   * Create a new tool
   */
  async createTool(request: CreateToolRequest): Promise<Tool> {
    try {
      const response = await apiClient.post('/api/tools', request)
      return response.data
    } catch (error) {
      console.error('Failed to create tool:', error)
      throw error
    }
  }

  /**
   * Update an existing tool
   */
  async updateTool(name: string, updates: Partial<CreateToolRequest>): Promise<Tool> {
    try {
      const response = await apiClient.put(`/api/tools/${name}`, updates)
      return response.data
    } catch (error) {
      console.error(`Failed to update tool ${name}:`, error)
      throw error
    }
  }

  /**
   * Delete a tool
   */
  async deleteTool(name: string): Promise<void> {
    try {
      await apiClient.delete(`/api/tools/${name}`)
    } catch (error) {
      console.error(`Failed to delete tool ${name}:`, error)
      throw error
    }
  }

  /**
   * Get tool execution history
   */
  async getToolExecutions(toolName?: string): Promise<ToolExecution[]> {
    try {
      const params = toolName ? { tool_name: toolName } : {}
      const response = await apiClient.get('/api/tools/executions', { params })
      return response.data
    } catch (error) {
      console.error('Failed to fetch tool executions:', error)
      throw error
    }
  }

  /**
   * Get tool categories
   */
  async getToolCategories(): Promise<string[]> {
    try {
      const response = await apiClient.get('/api/tools/categories')
      return response.data
    } catch (error) {
      console.error('Failed to fetch tool categories:', error)
      throw error
    }
  }

  /**
   * Validate tool specification
   */
  async validateToolSpec(spec: ToolSpec): Promise<{ valid: boolean; errors?: string[] }> {
    try {
      const response = await apiClient.post('/api/tools/validate', spec)
      return response.data
    } catch (error) {
      console.error('Failed to validate tool spec:', error)
      throw error
    }
  }
}

export const toolsApi = new ToolsApiService()