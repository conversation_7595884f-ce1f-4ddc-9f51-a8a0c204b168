"""
Simple migration script to add tool tracking fields to the database.
"""

import sqlite3
import os
from loguru import logger

def run_migration():
    """Run the migration to add tool tracking fields."""
    db_path = "data/agentkit.sqlite"
    
    if not os.path.exists(db_path):
        logger.error(f"Database file not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Add new columns to tool_registry table
        migrations = [
            # Tool metadata fields
            "ALTER TABLE tool_registry ADD COLUMN output_schema TEXT",
            "ALTER TABLE tool_registry ADD COLUMN version VARCHAR(20)",
            "ALTER TABLE tool_registry ADD COLUMN author VARCHAR(100)",
            "ALTER TABLE tool_registry ADD COLUMN tags TEXT",
            
            # Usage tracking fields
            "ALTER TABLE tool_registry ADD COLUMN usage_count INTEGER DEFAULT 0",
            "ALTER TABLE tool_registry ADD COLUMN last_used TIMESTAMP",
            "ALTER TABLE tool_registry ADD COLUMN execution_count INTEGER DEFAULT 0",
            "ALTER TABLE tool_registry ADD COLUMN success_count INTEGER DEFAULT 0",
            "ALTER TABLE tool_registry ADD COLUMN error_count INTEGER DEFAULT 0",
            
            # Performance metrics fields
            "ALTER TABLE tool_registry ADD COLUMN avg_execution_time REAL",
            "ALTER TABLE tool_registry ADD COLUMN total_execution_time REAL DEFAULT 0.0",
        ]
        
        for migration in migrations:
            try:
                cursor.execute(migration)
                logger.info(f"Executed migration: {migration}")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e).lower():
                    logger.warning(f"Column already exists: {migration}")
                else:
                    logger.error(f"Migration failed: {migration} - {e}")
        
        # Create tool execution history table
        create_history_table = """
        CREATE TABLE IF NOT EXISTS tool_execution_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            execution_id VARCHAR(100) UNIQUE NOT NULL,
            tool_id INTEGER,
            tool_name VARCHAR(100) NOT NULL,
            input_parameters TEXT NOT NULL,
            output_result TEXT,
            success BOOLEAN NOT NULL,
            error_message TEXT,
            execution_time REAL NOT NULL,
            memory_usage REAL,
            user_id VARCHAR(100),
            session_id VARCHAR(100),
            agent_id VARCHAR(100),
            started_at TIMESTAMP NOT NULL,
            completed_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (tool_id) REFERENCES tool_registry (id)
        )
        """
        
        cursor.execute(create_history_table)
        logger.info("Created tool_execution_history table")
        
        conn.commit()
        conn.close()
        
        logger.info("Tool tracking fields migration completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        return False


if __name__ == "__main__":
    logger.info("Running tool tracking fields migration...")
    success = run_migration()
    if success:
        logger.info("Migration completed successfully!")
    else:
        logger.error("Migration failed!")
